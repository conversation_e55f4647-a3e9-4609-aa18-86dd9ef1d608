#!/usr/bin/env python3
"""
Script de démonstration pour ajouter des données d'exemple
"""

import sys
import os
from datetime import date

# Ajouter le répertoire src au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from models.secteur import SecteurModel
from models.unite import UniteModel
from models.categorie_piece import CategoriePieceModel
from models.piece import PieceModel
from models.periode import PeriodeModel
from models.consommation import ConsommationModel

def add_demo_data():
    """Ajoute des données de démonstration"""
    print("Ajout de données de démonstration...")
    
    try:
        # Modèles
        secteur_model = SecteurModel()
        unite_model = UniteModel()
        categorie_model = CategoriePieceModel()
        piece_model = PieceModel()
        periode_model = PeriodeModel()
        consommation_model = ConsommationModel()
        
        # Ajouter des secteurs de démonstration
        print("Ajout de secteurs...")
        secteurs_demo = [
            "Secteur Nord",
            "Secteur Sud", 
            "Secteur Est",
            "Secteur Ouest"
        ]
        
        secteur_ids = []
        for nom in secteurs_demo:
            # Vérifier si le secteur existe déjà
            existing = secteur_model.search_by_name(nom)
            if not existing:
                secteur_id = secteur_model.create(nom)
                if secteur_id:
                    secteur_ids.append(secteur_id)
                    print(f"  ✓ Secteur créé: {nom}")
                else:
                    print(f"  ✗ Erreur création secteur: {nom}")
            else:
                secteur_ids.append(existing[0]['id_secteur'])
                print(f"  - Secteur existant: {nom}")
        
        # Ajouter des unités de démonstration
        print("Ajout d'unités...")
        unites_demo = [
            ("Unité Alpha", 0),
            ("Unité Beta", 0),
            ("Unité Gamma", 1),
            ("Unité Delta", 1),
            ("Unité Echo", 2),
            ("Unité Foxtrot", 3)
        ]
        
        unite_ids = []
        for nom, secteur_idx in unites_demo:
            if secteur_idx < len(secteur_ids):
                existing = unite_model.search_by_name(nom)
                if not existing:
                    unite_id = unite_model.create(nom, secteur_ids[secteur_idx])
                    if unite_id:
                        unite_ids.append(unite_id)
                        print(f"  ✓ Unité créée: {nom}")
                    else:
                        print(f"  ✗ Erreur création unité: {nom}")
                else:
                    unite_ids.append(existing[0]['id_unite'])
                    print(f"  - Unité existante: {nom}")
        
        # Ajouter des catégories de démonstration
        print("Ajout de catégories...")
        categories_demo = ["Petite C", "Grande C"]
        
        categorie_ids = []
        for nom in categories_demo:
            existing = categorie_model.get_all()
            existing_names = [cat['nom_categorie'] for cat in existing]
            if nom not in existing_names:
                categorie_id = categorie_model.create(nom)
                if categorie_id:
                    categorie_ids.append(categorie_id)
                    print(f"  ✓ Catégorie créée: {nom}")
                else:
                    print(f"  ✗ Erreur création catégorie: {nom}")
            else:
                # Trouver l'ID de la catégorie existante
                for cat in existing:
                    if cat['nom_categorie'] == nom:
                        categorie_ids.append(cat['id_categorie'])
                        break
                print(f"  - Catégorie existante: {nom}")
        
        # Ajouter des pièces de démonstration
        print("Ajout de pièces...")
        pieces_demo = [
            ("Cartouches 5.56mm", 0),
            ("Cartouches 7.62mm", 0),
            ("Grenades défensives", 1),
            ("Grenades offensives", 1),
            ("Munitions 9mm", 0),
            ("Fusées éclairantes", 1)
        ]
        
        piece_ids = []
        for nom, cat_idx in pieces_demo:
            if cat_idx < len(categorie_ids):
                existing = piece_model.search_by_name(nom)
                if not existing:
                    piece_id = piece_model.create(nom, categorie_ids[cat_idx])
                    if piece_id:
                        piece_ids.append(piece_id)
                        print(f"  ✓ Pièce créée: {nom}")
                    else:
                        print(f"  ✗ Erreur création pièce: {nom}")
                else:
                    piece_ids.append(existing[0]['id_piece'])
                    print(f"  - Pièce existante: {nom}")
        
        # Ajouter des périodes de démonstration
        print("Ajout de périodes...")
        periodes_demo = [
            (date(2024, 1, 1), 1),
            (date(2024, 2, 1), 2),
            (date(2024, 3, 1), 3),
            (date(2024, 4, 1), 4)
        ]
        
        periode_ids = []
        for date_periode, mois in periodes_demo:
            # Vérifier si la période existe déjà
            existing = periode_model.get_by_month(mois)
            existing_dates = [p['date_periode'] for p in existing if p['date_periode'] == date_periode]
            if not existing_dates:
                periode_id = periode_model.create(date_periode, mois)
                if periode_id:
                    periode_ids.append(periode_id)
                    print(f"  ✓ Période créée: {date_periode} (mois {mois})")
                else:
                    print(f"  ✗ Erreur création période: {date_periode}")
            else:
                # Trouver l'ID de la période existante
                for p in existing:
                    if p['date_periode'] == date_periode:
                        periode_ids.append(p['id_periode'])
                        break
                print(f"  - Période existante: {date_periode}")
        
        # Ajouter des consommations de démonstration
        print("Ajout de consommations...")
        if unite_ids and piece_ids and periode_ids:
            consommations_demo = [
                (0, 0, 0, 1000, 750),  # Unité Alpha, Cartouches 5.56mm, Janvier
                (0, 1, 0, 500, 400),   # Unité Alpha, Cartouches 7.62mm, Janvier
                (1, 0, 1, 800, 600),   # Unité Beta, Cartouches 5.56mm, Février
                (2, 2, 1, 200, 150),   # Unité Gamma, Grenades défensives, Février
                (3, 4, 2, 300, 250),   # Unité Delta, Munitions 9mm, Mars
            ]
            
            for unite_idx, piece_idx, periode_idx, attribue, consomme in consommations_demo:
                if (unite_idx < len(unite_ids) and 
                    piece_idx < len(piece_ids) and 
                    periode_idx < len(periode_ids)):
                    
                    consommation_id = consommation_model.create(
                        unite_ids[unite_idx],
                        piece_ids[piece_idx],
                        periode_ids[periode_idx],
                        attribue,
                        consomme
                    )
                    
                    if consommation_id:
                        reste = attribue - consomme
                        print(f"  ✓ Consommation créée: {attribue} attribués, {consomme} consommés, {reste} restants")
                    else:
                        print(f"  ✗ Erreur création consommation")
        
        print("\n✓ Données de démonstration ajoutées avec succès!")
        print("\nVous pouvez maintenant tester l'application avec ces données.")
        
    except Exception as e:
        print(f"✗ Erreur lors de l'ajout des données: {e}")

def show_summary():
    """Affiche un résumé des données"""
    print("\n=== Résumé des données ===")
    
    try:
        from models.secteur import SecteurModel
        from models.unite import UniteModel
        from models.categorie_piece import CategoriePieceModel
        from models.piece import PieceModel
        from models.periode import PeriodeModel
        from models.consommation import ConsommationModel
        
        secteur_model = SecteurModel()
        unite_model = UniteModel()
        categorie_model = CategoriePieceModel()
        piece_model = PieceModel()
        periode_model = PeriodeModel()
        consommation_model = ConsommationModel()
        
        secteurs = secteur_model.get_all()
        unites = unite_model.get_all()
        categories = categorie_model.get_all()
        pieces = piece_model.get_all()
        periodes = periode_model.get_all()
        consommations = consommation_model.get_all()
        
        print(f"Secteurs: {len(secteurs)}")
        print(f"Unités: {len(unites)}")
        print(f"Catégories: {len(categories)}")
        print(f"Pièces: {len(pieces)}")
        print(f"Périodes: {len(periodes)}")
        print(f"Consommations: {len(consommations)}")
        
    except Exception as e:
        print(f"Erreur lors de l'affichage du résumé: {e}")

def main():
    """Fonction principale"""
    print("=== Ajout de données de démonstration ===\n")
    
    response = input("Voulez-vous ajouter des données de démonstration? (o/n): ")
    if response.lower() in ['o', 'oui', 'y', 'yes']:
        add_demo_data()
        show_summary()
        
        print("\nVous pouvez maintenant lancer l'application:")
        print("  python app_tkinter.py")
        print("  ou")
        print("  python main.py")
    else:
        print("Opération annulée.")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Application Gestion MUN avec interface Tkinter
Version alternative en cas de problème avec PyQt6/PySide6
"""

import sys
import os
import tkinter as tk
from tkinter import ttk, messagebox
from typing import List, Dict, Any

# Ajouter le répertoire src au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from models.secteur import SecteurModel
from models.unite import UniteModel
from models.categorie_piece import CategoriePieceModel
from models.piece import PieceModel
from models.periode import PeriodeModel
from models.consommation import ConsommationModel

class BaseFrame(ttk.Frame):
    """Frame de base pour tous les onglets"""
    
    def __init__(self, parent, title: str, model):
        super().__init__(parent)
        self.title = title
        self.model = model
        self.data = []
        self.init_ui()
        self.refresh_data()
    
    def init_ui(self):
        """Initialise l'interface utilisateur"""
        # Titre
        title_label = ttk.Label(self, text=f"Gestion des {self.title}", 
                               font=('Arial', 14, 'bold'))
        title_label.pack(pady=10)
        
        # Frame pour les boutons
        button_frame = ttk.Frame(self)
        button_frame.pack(fill='x', padx=10, pady=5)
        
        ttk.Button(button_frame, text="Ajouter", command=self.add_item).pack(side='left', padx=5)
        ttk.Button(button_frame, text="Modifier", command=self.edit_item).pack(side='left', padx=5)
        ttk.Button(button_frame, text="Supprimer", command=self.delete_item).pack(side='left', padx=5)
        ttk.Button(button_frame, text="Actualiser", command=self.refresh_data).pack(side='right', padx=5)
        
        # Frame pour la recherche
        search_frame = ttk.Frame(self)
        search_frame.pack(fill='x', padx=10, pady=5)
        
        ttk.Label(search_frame, text="Rechercher:").pack(side='left')
        self.search_var = tk.StringVar()
        self.search_var.trace('w', self.on_search)
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var)
        search_entry.pack(side='left', fill='x', expand=True, padx=5)
        
        # Treeview pour afficher les données
        self.tree = ttk.Treeview(self)
        self.tree.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Scrollbar pour le treeview
        scrollbar = ttk.Scrollbar(self, orient='vertical', command=self.tree.yview)
        scrollbar.pack(side='right', fill='y')
        self.tree.configure(yscrollcommand=scrollbar.set)
    
    def refresh_data(self):
        """Actualise les données"""
        try:
            self.data = self.model.get_all()
            self.populate_tree()
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du chargement: {str(e)}")
    
    def populate_tree(self):
        """Remplit le treeview avec les données"""
        # Effacer les données existantes
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        if not self.data:
            return
        
        # Configurer les colonnes
        columns = list(self.data[0].keys())
        self.tree['columns'] = columns
        self.tree['show'] = 'headings'
        
        # Configurer les en-têtes
        for col in columns:
            self.tree.heading(col, text=col.replace('_', ' ').title())
            self.tree.column(col, width=100)
        
        # Ajouter les données
        for item in self.data:
            values = [str(item.get(col, '')) for col in columns]
            self.tree.insert('', 'end', values=values)
    
    def on_search(self, *args):
        """Filtre les données selon la recherche"""
        search_term = self.search_var.get().lower()
        
        # Effacer les données existantes
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        if not self.data:
            return
        
        # Filtrer et afficher les données
        columns = list(self.data[0].keys())
        for item in self.data:
            # Vérifier si le terme de recherche est dans l'un des champs
            if any(search_term in str(item.get(col, '')).lower() for col in columns):
                values = [str(item.get(col, '')) for col in columns]
                self.tree.insert('', 'end', values=values)
    
    def get_selected_item(self):
        """Récupère l'élément sélectionné"""
        selection = self.tree.selection()
        if not selection:
            return None
        
        item = self.tree.item(selection[0])
        values = item['values']
        
        if not values or not self.data:
            return None
        
        # Trouver l'élément correspondant dans les données
        columns = list(self.data[0].keys())
        for data_item in self.data:
            if str(data_item.get(columns[0], '')) == str(values[0]):
                return data_item
        
        return None
    
    def add_item(self):
        """Ajoute un nouvel élément"""
        messagebox.showinfo("Info", "Fonctionnalité à implémenter")
    
    def edit_item(self):
        """Modifie l'élément sélectionné"""
        selected = self.get_selected_item()
        if not selected:
            messagebox.showwarning("Attention", "Veuillez sélectionner un élément à modifier")
            return
        messagebox.showinfo("Info", f"Modification de: {selected}")
    
    def delete_item(self):
        """Supprime l'élément sélectionné"""
        selected = self.get_selected_item()
        if not selected:
            messagebox.showwarning("Attention", "Veuillez sélectionner un élément à supprimer")
            return
        
        if messagebox.askyesno("Confirmation", "Êtes-vous sûr de vouloir supprimer cet élément?"):
            try:
                # Récupérer l'ID (première colonne)
                columns = list(selected.keys())
                id_value = selected[columns[0]]
                
                if self.model.delete(id_value):
                    messagebox.showinfo("Succès", "Élément supprimé avec succès")
                    self.refresh_data()
                else:
                    messagebox.showerror("Erreur", "Erreur lors de la suppression")
            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors de la suppression: {str(e)}")

class UniteFrame(BaseFrame):
    """Frame personnalisée pour les Unités, sans affichage de l'id secteur et autocomplétion du nom secteur"""

    def __init__(self, parent, title: str, model, secteur_model):
        self.secteur_model = secteur_model
        super().__init__(parent, title, model)

    def populate_tree(self):
        """Remplit le treeview avec les données, sans afficher id_secteur"""
        for item in self.tree.get_children():
            self.tree.delete(item)

        if not self.data:
            return

        # Exclure la colonne 'id_secteur'
        columns = [col for col in self.data[0].keys() if col != 'id_secteur']
        self.tree['columns'] = columns
        self.tree['show'] = 'headings'

        for col in columns:
            self.tree.heading(col, text=col.replace('_', ' ').title())
            self.tree.column(col, width=120)

        for item in self.data:
            values = [str(item.get(col, '')) for col in columns]
            self.tree.insert('', 'end', values=values)

    def add_item(self):
        """Ajoute une nouvelle unité avec autocomplétion du nom secteur"""
        def save():
            nom = entry_nom.get().strip()
            secteur_nom = entry_secteur.get().strip()
            if not nom or not secteur_nom:
                messagebox.showwarning("Attention", "Veuillez remplir tous les champs")
                return
            # Trouver l'id du secteur correspondant au nom
            secteurs = self.secteur_model.get_all()
            secteur_id = None
            for s in secteurs:
                if s.get('nom', '').lower() == secteur_nom.lower():
                    secteur_id = s.get('id')
                    break
            if not secteur_id:
                messagebox.showerror("Erreur", "Secteur non trouvé")
                return
            try:
                self.model.add({'nom': nom, 'id_secteur': secteur_id})
                top.destroy()
                self.refresh_data()
            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors de l'ajout: {str(e)}")

        top = tk.Toplevel(self)
        top.title("Ajouter une unité")
        tk.Label(top, text="Nom:").grid(row=0, column=0, padx=5, pady=5)
        entry_nom = ttk.Entry(top)
        entry_nom.grid(row=0, column=1, padx=5, pady=5)

        tk.Label(top, text="Nom Secteur:").grid(row=1, column=0, padx=5, pady=5)
        entry_secteur = ttk.Combobox(top)
        secteurs = self.secteur_model.get_all()
        entry_secteur['values'] = [s.get('nom', '') for s in secteurs]
        entry_secteur.grid(row=1, column=1, padx=5, pady=5)

        ttk.Button(top, text="Ajouter", command=save).grid(row=2, column=0, columnspan=2, pady=10)

class MainApplication:
    """Application principale"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Gestion MUN")
        self.root.geometry("1000x700")
        
        # Barre de statut
        self.status_bar = ttk.Label(self.root, text="Prêt", relief='sunken')
        self.status_bar.pack(side='bottom', fill='x')

        # Créer le notebook (onglets)
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill='both', expand=True, padx=10, pady=10)

        # Créer les onglets
        self.create_tabs()
    
    def create_tabs(self):
        """Crée les onglets pour chaque table"""
        try:
            # Onglet Secteurs
            secteur_model = SecteurModel()
            secteur_frame = BaseFrame(self.notebook, "Secteurs", secteur_model)
            self.notebook.add(secteur_frame, text="Secteurs")
            
            # Onglet Unités (personnalisé)
            unite_frame = UniteFrame(self.notebook, "Unités", UniteModel(), secteur_model)
            self.notebook.add(unite_frame, text="Unités")
            
            # Onglet Pièces
            piece_frame = BaseFrame(self.notebook, "Pièces", PieceModel())
            self.notebook.add(piece_frame, text="Pièces")
            
            # Onglet Périodes
            periode_frame = BaseFrame(self.notebook, "Périodes", PeriodeModel())
            self.notebook.add(periode_frame, text="Périodes")
            
            # Onglet Consommations
            consommation_frame = BaseFrame(self.notebook, "Consommations", ConsommationModel())
            self.notebook.add(consommation_frame, text="Consommations")
            
            self.status_bar.config(text="Application chargée avec succès")
            
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de la création des onglets: {str(e)}")
            self.status_bar.config(text="Erreur lors du chargement")
    
    def run(self):
        """Lance l'application"""
        self.root.mainloop()

def main():
    """Fonction principale"""
    try:
        app = MainApplication()
        app.run()
    except Exception as e:
        print(f"Erreur lors du lancement de l'application: {e}")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Test simple de l'application
"""

import sys
import os

# Ajouter le répertoire src au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_imports():
    """Teste les imports"""
    print("Test des imports...")
    
    try:
        from qt_imports import QApplication, QWidget, QVBoxLayout, QLabel, QPushButton
        print("✓ Imports Qt réussis")
        return True
    except Exception as e:
        print(f"✗ Erreur d'import Qt: {e}")
        return False

def test_database():
    """Teste la connexion à la base de données"""
    print("Test de la base de données...")
    
    try:
        from database.connection import db_connection
        if db_connection.connect():
            print("✓ Connexion à la base de données réussie")
            db_connection.disconnect()
            return True
        else:
            print("✗ Échec de la connexion à la base de données")
            return False
    except Exception as e:
        print(f"✗ Erreur de base de données: {e}")
        return False

def test_models():
    """Teste les modèles"""
    print("Test des modèles...")
    
    try:
        from models.secteur import SecteurModel
        from models.unite import UniteModel
        from models.categorie_piece import CategoriePieceModel
        from models.piece import PieceModel
        from models.periode import PeriodeModel
        from models.consommation import ConsommationModel
        
        print("✓ Tous les modèles importés avec succès")
        return True
    except Exception as e:
        print(f"✗ Erreur d'import des modèles: {e}")
        return False

def create_simple_window():
    """Crée une fenêtre simple pour tester"""
    try:
        from qt_imports import QApplication, QWidget, QVBoxLayout, QLabel, QPushButton
        
        app = QApplication(sys.argv)
        
        window = QWidget()
        window.setWindowTitle("Test Gestion MUN")
        window.resize(400, 300)
        
        layout = QVBoxLayout()
        
        label = QLabel("Application Gestion MUN")
        label.setStyleSheet("font-size: 18px; font-weight: bold; margin: 20px;")
        
        button = QPushButton("Test réussi!")
        button.clicked.connect(lambda: print("Bouton cliqué!"))
        
        layout.addWidget(label)
        layout.addWidget(button)
        
        window.setLayout(layout)
        window.show()
        
        print("✓ Fenêtre de test créée avec succès")
        print("Fermez la fenêtre pour continuer...")
        
        return app.exec()
        
    except Exception as e:
        print(f"✗ Erreur lors de la création de la fenêtre: {e}")
        return False

def main():
    """Fonction principale de test"""
    print("=== Test simple de l'application Gestion MUN ===\n")
    
    # Test des imports
    if not test_imports():
        return
    
    # Test de la base de données
    if not test_database():
        print("Veuillez vérifier votre configuration de base de données")
        return
    
    # Test des modèles
    if not test_models():
        return
    
    print("\n✓ Tous les tests de base sont passés!")
    
    # Test de l'interface
    response = input("\nVoulez-vous tester l'interface graphique? (o/n): ")
    if response.lower() in ['o', 'oui', 'y', 'yes']:
        create_simple_window()
    
    print("\nTest terminé.")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Debug des champs des unités pour corriger la redondance
"""

import sys
import os

# Ajouter le répertoire src au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def debug_unite_fields():
    """Debug des champs des unités"""
    print("=== Debug des champs des unités ===")
    
    try:
        from models.unite import UniteModel
        from models.secteur import SecteurModel
        
        unite_model = UniteModel()
        secteur_model = SecteurModel()
        
        # Vérifier les unités
        unites = unite_model.get_all()
        print(f"Nombre d'unités: {len(unites)}")
        
        if unites:
            print("\nPremière unité - tous les champs:")
            first_unite = unites[0]
            for key, value in first_unite.items():
                print(f"  {key}: {value}")
            
            print(f"\nChamps disponibles: {list(first_unite.keys())}")
        
        # Vérifier les secteurs
        secteurs = secteur_model.get_all()
        print(f"\nNombre de secteurs: {len(secteurs)}")
        
        if secteurs:
            print("\nPremier secteur - tous les champs:")
            first_secteur = secteurs[0]
            for key, value in first_secteur.items():
                print(f"  {key}: {value}")
            
            print(f"\nChamps disponibles: {list(first_secteur.keys())}")
        
    except Exception as e:
        print(f"Erreur: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_unite_fields()

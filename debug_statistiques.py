#!/usr/bin/env python3
"""
Debug des problèmes de l'onglet Statistiques
"""

import sys
import os

# Ajouter le répertoire src au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_matplotlib_import():
    """Teste l'importation de matplotlib"""
    print("=== Test d'importation de matplotlib ===")
    
    try:
        import matplotlib
        print(f"✅ matplotlib version: {matplotlib.__version__}")
        
        import matplotlib.pyplot as plt
        print("✅ matplotlib.pyplot importé avec succès")
        
        from matplotlib.backends.backend_tkagg import FigureCanvasTkinter
        print("✅ FigureCanvasTkinter importé avec succès")
        
        # Test de création d'un graphique simple
        fig, ax = plt.subplots(figsize=(6, 4))
        ax.plot([1, 2, 3], [1, 4, 2])
        ax.set_title("Test Graph")
        print("✅ Création de graphique réussie")
        
        plt.close(fig)  # Nettoyer
        return True
        
    except ImportError as e:
        print(f"❌ Erreur d'importation: {e}")
        return False
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_data_availability():
    """Teste la disponibilité des données pour les statistiques"""
    print("\n=== Test de disponibilité des données ===")
    
    try:
        from models.consommation import ConsommationModel
        from models.unite import UniteModel
        from models.secteur import SecteurModel
        from models.piece import PieceModel
        from models.periode import PeriodeModel
        
        # Test des modèles
        consommation_model = ConsommationModel()
        unite_model = UniteModel()
        secteur_model = SecteurModel()
        piece_model = PieceModel()
        periode_model = PeriodeModel()
        
        # Récupérer les données
        consommations = consommation_model.get_all()
        unites = unite_model.get_all()
        secteurs = secteur_model.get_all()
        pieces = piece_model.get_all()
        periodes = periode_model.get_all()
        
        print(f"Consommations: {len(consommations)}")
        print(f"Unités: {len(unites)}")
        print(f"Secteurs: {len(secteurs)}")
        print(f"Pièces: {len(pieces)}")
        print(f"Périodes: {len(periodes)}")
        
        if len(consommations) > 0:
            print("\n✅ Données disponibles pour les statistiques")
            
            # Vérifier la structure des données de consommation
            first_cons = consommations[0]
            required_fields = ['nom_unite', 'nom_secteur', 'nom_piece', 'nom_categorie', 
                             'nombre_attribue', 'nombre_consomme', 'date_periode']
            
            missing_fields = []
            for field in required_fields:
                if field not in first_cons:
                    missing_fields.append(field)
            
            if missing_fields:
                print(f"⚠️ Champs manquants: {missing_fields}")
            else:
                print("✅ Tous les champs requis sont présents")
            
            return True
        else:
            print("❌ Aucune donnée de consommation disponible")
            return False
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_simple_graph_creation():
    """Teste la création d'un graphique simple avec les vraies données"""
    print("\n=== Test de création de graphique simple ===")
    
    try:
        import matplotlib
        matplotlib.use('Agg')  # Backend non-interactif pour le test
        import matplotlib.pyplot as plt
        
        from models.consommation import ConsommationModel
        
        consommation_model = ConsommationModel()
        consommations = consommation_model.get_all()
        
        if not consommations:
            print("❌ Aucune donnée pour créer le graphique")
            return False
        
        # Calculer les totaux par unité
        unite_totals = {}
        for cons in consommations:
            unite = cons.get('nom_unite', 'Inconnu')
            consomme = cons.get('nombre_consomme', 0)
            if unite in unite_totals:
                unite_totals[unite] += consomme
            else:
                unite_totals[unite] = consomme
        
        print(f"Données calculées pour {len(unite_totals)} unités")
        
        # Créer un graphique simple
        fig, ax = plt.subplots(figsize=(10, 6))
        unites = list(unite_totals.keys())[:5]  # Top 5 pour le test
        values = [unite_totals[u] for u in unites]
        
        bars = ax.bar(unites, values)
        ax.set_title('Test - Top 5 Consommation par Unité')
        ax.set_xlabel('Unités')
        ax.set_ylabel('Quantité Consommée')
        
        # Sauvegarder pour vérifier
        plt.savefig('test_graph.png', dpi=100, bbox_inches='tight')
        plt.close(fig)
        
        print("✅ Graphique créé et sauvegardé (test_graph.png)")
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la création du graphique: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_tkinter_integration():
    """Teste l'intégration avec Tkinter"""
    print("\n=== Test d'intégration Tkinter ===")
    
    try:
        import tkinter as tk
        import matplotlib
        matplotlib.use('TkAgg')  # Backend Tkinter
        import matplotlib.pyplot as plt
        from matplotlib.backends.backend_tkagg import FigureCanvasTkinter
        
        # Créer une fenêtre de test
        root = tk.Tk()
        root.title("Test Matplotlib + Tkinter")
        root.geometry("600x400")
        
        # Créer un graphique simple
        fig, ax = plt.subplots(figsize=(6, 4))
        ax.plot([1, 2, 3, 4], [1, 4, 2, 3], 'bo-')
        ax.set_title('Test Graph in Tkinter')
        ax.set_xlabel('X')
        ax.set_ylabel('Y')
        
        # Intégrer dans Tkinter
        canvas = FigureCanvasTkinter(fig, root)
        canvas.draw()
        canvas.get_tk_widget().pack(fill='both', expand=True)
        
        # Fermer automatiquement après 2 secondes
        root.after(2000, root.destroy)
        root.mainloop()
        
        print("✅ Intégration Tkinter réussie")
        return True
        
    except Exception as e:
        print(f"❌ Erreur d'intégration Tkinter: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Fonction principale"""
    print("=== Diagnostic des problèmes de l'onglet Statistiques ===\n")
    
    results = []
    results.append(test_matplotlib_import())
    results.append(test_data_availability())
    results.append(test_simple_graph_creation())
    results.append(test_tkinter_integration())
    
    success_count = sum(results)
    total_count = len(results)
    
    print(f"\n=== Résultats du diagnostic ===")
    print(f"Tests réussis: {success_count}/{total_count}")
    
    if success_count == total_count:
        print("\n✅ Tous les composants fonctionnent correctement!")
        print("Le problème pourrait être dans l'implémentation de l'onglet Statistiques.")
        print("\nSolutions possibles:")
        print("1. Vérifier les imports dans app_tkinter.py")
        print("2. Corriger la gestion d'erreurs dans StatistiquesFrame")
        print("3. S'assurer que matplotlib utilise le bon backend")
    else:
        print(f"\n❌ {total_count - success_count} problème(s) détecté(s)")
        print("\nActions recommandées:")
        if not results[0]:
            print("- Installer matplotlib: pip install matplotlib")
        if not results[1]:
            print("- Vérifier la base de données et les modèles")
        if not results[2]:
            print("- Corriger la logique de création de graphiques")
        if not results[3]:
            print("- Corriger l'intégration Tkinter")

if __name__ == "__main__":
    main()

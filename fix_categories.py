#!/usr/bin/env python3
"""
Script pour corriger les catégories de pièces
"""

import sys
import os

# Ajouter le répertoire src au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def fix_categories():
    """Corrige les catégories de pièces"""
    print("=== Correction des catégories ===")
    
    try:
        from database.connection import db_connection
        
        if not db_connection.connect():
            print("Erreur de connexion à la base de données")
            return False
        
        # Option 1: Mettre à jour les catégories 1 et 2 avec des noms
        print("Mise à jour des catégories 1 et 2...")
        
        # Mettre à jour la catégorie 1
        query1 = "UPDATE CategoriePiece SET nom_categorie = 'Petite C' WHERE id_categorie = 1"
        success1 = db_connection.execute_update(query1)
        
        # Mettre à jour la catégorie 2  
        query2 = "UPDATE CategoriePiece SET nom_categorie = 'Grande C' WHERE id_categorie = 2"
        success2 = db_connection.execute_update(query2)
        
        if success1 and success2:
            print("✅ Catégories 1 et 2 mises à jour avec succès")
            
            # Vérifier le résultat
            print("\nVérification des catégories:")
            query_check = "SELECT id_categorie, nom_categorie FROM CategoriePiece WHERE id_categorie IN (1, 2)"
            result = db_connection.execute_query(query_check)
            
            for row in result:
                print(f"  ID: {row['id_categorie']}, Nom: '{row['nom_categorie']}'")
            
            # Tester une pièce
            print("\nTest d'une pièce avec catégorie:")
            query_piece = """
            SELECT p.nom_piece, p.id_categorie, c.nom_categorie
            FROM Piece p
            LEFT JOIN CategoriePiece c ON p.id_categorie = c.id_categorie
            WHERE p.id_categorie IN (1, 2)
            LIMIT 3
            """
            pieces = db_connection.execute_query(query_piece)
            
            for piece in pieces:
                print(f"  Pièce: {piece['nom_piece']}, Catégorie: {piece['nom_categorie']}")
            
            db_connection.disconnect()
            return True
        else:
            print("❌ Erreur lors de la mise à jour des catégories")
            db_connection.disconnect()
            return False
            
    except Exception as e:
        print(f"Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_after_fix():
    """Teste les modèles après la correction"""
    print("\n=== Test après correction ===")
    
    try:
        from models.piece import PieceModel
        
        piece_model = PieceModel()
        pieces = piece_model.get_all()
        
        print(f"Nombre de pièces: {len(pieces)}")
        print("Premières pièces avec catégories:")
        
        for i, piece in enumerate(pieces[:5]):
            nom_categorie = piece.get('nom_categorie', 'N/A')
            print(f"  {i+1}. {piece['nom_piece']} - Catégorie: {nom_categorie}")
        
        return True
    except Exception as e:
        print(f"Erreur lors du test: {e}")
        return False

def main():
    """Fonction principale"""
    print("=== Correction des catégories de pièces ===\n")
    
    response = input("Voulez-vous corriger les catégories? (o/n): ")
    if response.lower() in ['o', 'oui', 'y', 'yes']:
        if fix_categories():
            test_after_fix()
            print("\n✅ Correction terminée avec succès!")
            print("Vous pouvez maintenant relancer l'application pour voir les noms de catégories.")
        else:
            print("\n❌ Erreur lors de la correction")
    else:
        print("Opération annulée.")

if __name__ == "__main__":
    main()

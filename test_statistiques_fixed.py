#!/usr/bin/env python3
"""
Test des statistiques après correction des imports matplotlib
"""

import sys
import os

# Ajouter le répertoire src au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_matplotlib_fixed():
    """Teste si matplotlib fonctionne maintenant"""
    print("=== Test matplotlib après correction ===")
    
    try:
        import matplotlib
        matplotlib.use('TkAgg')
        import matplotlib.pyplot as plt
        from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
        
        print(f"✅ matplotlib version: {matplotlib.__version__}")
        print("✅ FigureCanvasTkAgg importé avec succès")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_simple_statistique():
    """Teste la création d'un graphique simple avec les vraies données"""
    print("\n=== Test de création de graphique ===")
    
    try:
        import matplotlib
        matplotlib.use('TkAgg')
        import matplotlib.pyplot as plt
        from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
        import tkinter as tk
        
        from models.consommation import ConsommationModel
        
        # Récupérer les données
        consommation_model = ConsommationModel()
        consommations = consommation_model.get_all()
        
        if not consommations:
            print("❌ Aucune donnée disponible")
            return False
        
        print(f"Données disponibles: {len(consommations)} consommations")
        
        # Calculer les totaux par unité
        unite_totals = {}
        for cons in consommations:
            unite = cons.get('nom_unite', 'Inconnu')
            consomme = cons.get('nombre_consomme', 0)
            if unite in unite_totals:
                unite_totals[unite] += consomme
            else:
                unite_totals[unite] = consomme
        
        # Prendre le top 5
        sorted_unites = sorted(unite_totals.items(), key=lambda x: x[1], reverse=True)[:5]
        unites = [item[0] for item in sorted_unites]
        values = [item[1] for item in sorted_unites]
        
        print(f"Top 5 unités:")
        for i, (unite, value) in enumerate(sorted_unites):
            print(f"  {i+1}. {unite}: {value}")
        
        # Créer une fenêtre de test
        root = tk.Tk()
        root.title("Test Statistiques")
        root.geometry("800x600")
        
        # Créer le graphique
        fig, ax = plt.subplots(figsize=(10, 6))
        bars = ax.bar(unites, values, color='skyblue', edgecolor='navy')
        ax.set_title('Test - Top 5 Consommation par Unité', fontsize=14, fontweight='bold')
        ax.set_xlabel('Unités')
        ax.set_ylabel('Quantité Consommée')
        ax.tick_params(axis='x', rotation=45)
        
        # Ajouter les valeurs sur les barres
        for bar, value in zip(bars, values):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                   f'{value}', ha='center', va='bottom')
        
        plt.tight_layout()
        
        # Intégrer dans Tkinter
        canvas = FigureCanvasTkAgg(fig, root)
        canvas.draw()
        canvas.get_tk_widget().pack(fill='both', expand=True)
        
        # Ajouter un bouton pour fermer
        btn_close = tk.Button(root, text="Fermer", command=root.destroy)
        btn_close.pack(pady=10)
        
        print("✅ Graphique créé avec succès!")
        print("Une fenêtre de test va s'ouvrir avec le graphique.")
        print("Fermez la fenêtre pour continuer...")
        
        # Afficher la fenêtre
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_statistiques_class():
    """Teste la classe StatistiquesFrame directement"""
    print("\n=== Test de la classe StatistiquesFrame ===")
    
    try:
        import tkinter as tk
        from models.consommation import ConsommationModel
        from models.unite import UniteModel
        from models.secteur import SecteurModel
        from models.piece import PieceModel
        from models.periode import PeriodeModel
        
        # Importer la classe depuis app_tkinter
        sys.path.insert(0, '.')
        from app_tkinter import StatistiquesFrame
        
        # Créer une fenêtre de test
        root = tk.Tk()
        root.title("Test StatistiquesFrame")
        root.geometry("1000x700")
        
        # Créer les modèles
        consommation_model = ConsommationModel()
        unite_model = UniteModel()
        secteur_model = SecteurModel()
        piece_model = PieceModel()
        periode_model = PeriodeModel()
        
        # Créer le frame de statistiques
        stats_frame = StatistiquesFrame(
            root,
            consommation_model,
            unite_model,
            secteur_model,
            piece_model,
            periode_model
        )
        stats_frame.pack(fill='both', expand=True)
        
        print("✅ StatistiquesFrame créé avec succès!")
        print("Une fenêtre de test va s'ouvrir.")
        print("Testez les boutons de la sidebar pour voir les graphiques.")
        print("Fermez la fenêtre pour continuer...")
        
        # Afficher la fenêtre
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Fonction principale"""
    print("=== Test des statistiques après correction ===\n")
    
    results = []
    results.append(test_matplotlib_fixed())
    results.append(test_simple_statistique())
    results.append(test_statistiques_class())
    
    success_count = sum(results)
    total_count = len(results)
    
    print(f"\n=== Résultats ===")
    print(f"Tests réussis: {success_count}/{total_count}")
    
    if success_count == total_count:
        print("\n✅ Toutes les corrections fonctionnent!")
        print("L'onglet Statistiques devrait maintenant afficher les graphiques.")
        print("\nVous pouvez relancer l'application et tester l'onglet Statistiques.")
    else:
        print(f"\n⚠️ {total_count - success_count} test(s) ont échoué")

if __name__ == "__main__":
    main()

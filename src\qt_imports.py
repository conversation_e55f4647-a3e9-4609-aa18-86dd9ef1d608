"""
Module pour gérer l'importation de PyQt6 ou PySide6
"""

# Essayer d'importer PyQt6, sinon utiliser PySide6
try:
    from PyQt6.QtWidgets import *
    from PyQt6.QtCore import *
    from PyQt6.QtGui import *
    QT_FRAMEWORK = "PyQt6"
    print(f"Utilisation de {QT_FRAMEWORK}")
except ImportError:
    try:
        from PySide6.QtWidgets import *
        from PySide6.QtCore import *
        from PySide6.QtGui import *
        QT_FRAMEWORK = "PySide6"
        print(f"Utilisation de {QT_FRAMEWORK}")
    except ImportError:
        raise ImportError("Ni PyQt6 ni PySide6 ne sont disponibles")

# Fonction pour gérer les différences entre PyQt6 et PySide6
def get_dialog_code():
    """Retourne le code de dialogue approprié selon le framework"""
    if QT_FRAMEWORK == "PyQt6":
        return QDialog.DialogCode
    else:  # PySide6
        return QDialog

def get_message_box_buttons():
    """Retourne les boutons de message box appropriés selon le framework"""
    if QT_FRAMEWORK == "PyQt6":
        return QMessageBox.StandardButton
    else:  # PySide6
        return QMessageBox.StandardButton

def get_item_flags():
    """Retourne les flags d'items appropriés selon le framework"""
    if QT_FRAMEWORK == "PyQt6":
        return Qt.ItemFlag
    else:  # PySide6
        return Qt

def get_selection_behavior():
    """Retourne le comportement de sélection approprié selon le framework"""
    if QT_FRAMEWORK == "PyQt6":
        return QAbstractItemView.SelectionBehavior
    else:  # PySide6
        return QAbstractItemView

#!/usr/bin/env python3
"""
Application de gestion MUN
Interface PyQt6/PySide6 pour la gestion des tables de base de données
"""

import sys
import os

# Ajouter le répertoire src au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Importer les modules Qt
from qt_imports import QApplication, Qt
from ui.main_window import MainWindow

def main():
    """Point d'entrée principal de l'application"""
    app = QApplication(sys.argv)
    app.setApplicationName("Gestion MUN")
    app.setApplicationVersion("1.0")
    
    # Configuration du style
    app.setStyle('Fusion')
    
    # Créer et afficher la fenêtre principale
    window = MainWindow()
    window.show()
    
    return app.exec()

if __name__ == "__main__":
    sys.exit(main())

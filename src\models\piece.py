"""
Modèle pour la table Piece
"""

from typing import List, Dict, Any, Optional
from .base_model import BaseModel

class PieceModel(BaseModel):
    """Modèle pour la gestion des pièces"""
    
    @property
    def table_name(self) -> str:
        return "Piece"
    
    @property
    def primary_key(self) -> str:
        return "id_piece"
    
    def create(self, nom_piece: str, id_categorie: int) -> Optional[int]:
        """
        Crée une nouvelle pièce
        
        Args:
            nom_piece: Nom de la pièce
            id_categorie: ID de la catégorie
            
        Returns:
            ID de la pièce créée ou None en cas d'erreur
        """
        query = "INSERT INTO Piece (nom_piece, id_categorie) VALUES (%s, %s)"
        if self.db.execute_update(query, (nom_piece, id_categorie)):
            return self.db.get_last_insert_id()
        return None
    
    def update(self, id_piece: int, nom_piece: str, id_categorie: int) -> bool:
        """
        Met à jour une pièce
        
        Args:
            id_piece: ID de la pièce
            nom_piece: Nouveau nom de la pièce
            id_categorie: Nouvel ID de la catégorie
            
        Returns:
            True si la mise à jour est réussie
        """
        query = "UPDATE Piece SET nom_piece = %s, id_categorie = %s WHERE id_piece = %s"
        return self.db.execute_update(query, (nom_piece, id_categorie, id_piece))
    
    def get_all(self) -> List[Dict[str, Any]]:
        """
        Récupère toutes les pièces avec les informations de catégorie
        (Override de la méthode de base pour inclure les noms de catégories)

        Returns:
            Liste des pièces avec catégorie
        """
        return self.get_all_with_categorie()

    def get_all_with_categorie(self) -> List[Dict[str, Any]]:
        """
        Récupère toutes les pièces avec les informations de catégorie
        
        Returns:
            Liste des pièces avec catégorie
        """
        query = """
        SELECT p.id_piece, p.nom_piece, p.id_categorie, c.nom_categorie
        FROM Piece p
        LEFT JOIN CategoriePiece c ON p.id_categorie = c.id_categorie
        ORDER BY p.nom_piece
        """
        result = self.db.execute_query(query)
        return result or []
    
    def get_by_categorie(self, id_categorie: int) -> List[Dict[str, Any]]:
        """
        Récupère les pièces d'une catégorie spécifique
        
        Args:
            id_categorie: ID de la catégorie
            
        Returns:
            Liste des pièces de la catégorie
        """
        query = "SELECT * FROM Piece WHERE id_categorie = %s ORDER BY nom_piece"
        result = self.db.execute_query(query, (id_categorie,))
        return result or []
    
    def get_pieces_for_combobox(self) -> List[Dict[str, Any]]:
        """
        Récupère les pièces formatées pour les combobox
        
        Returns:
            Liste des pièces avec id et nom
        """
        return self.get_all_with_categorie()
    
    def search_by_name(self, name: str) -> List[Dict[str, Any]]:
        """
        Recherche les pièces par nom
        
        Args:
            name: Nom à rechercher
            
        Returns:
            Liste des pièces correspondantes
        """
        return self.search(name, ['nom_piece'])

#!/usr/bin/env python3
"""
Application Gestion MUN avec interface Tkinter
Version alternative en cas de problème avec PyQt6/PySide6
"""

import sys
import os
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from typing import List, Dict, Any
import csv
import json
from datetime import datetime

# Ajouter le répertoire src au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from models.secteur import SecteurModel
from models.unite import UniteModel
from models.categorie_piece import CategoriePieceModel
from models.piece import PieceModel
from models.periode import PeriodeModel
from models.consommation import ConsommationModel

class BaseFrame(ttk.Frame):
    """Frame de base pour tous les onglets"""
    
    def __init__(self, parent, title: str, model):
        super().__init__(parent)
        self.title = title
        self.model = model
        self.data = []
        self.init_ui()
        self.refresh_data()
    
    def init_ui(self):
        """Initialise l'interface utilisateur"""
        # Titre
        title_label = ttk.Label(self, text=f"Gestion des {self.title}", 
                               font=('Arial', 14, 'bold'))
        title_label.pack(pady=10)
        
        # Frame pour les boutons
        button_frame = ttk.Frame(self)
        button_frame.pack(fill='x', padx=10, pady=5)
        
        ttk.Button(button_frame, text="Ajouter", command=self.add_item).pack(side='left', padx=5)
        ttk.Button(button_frame, text="Modifier", command=self.edit_item).pack(side='left', padx=5)
        ttk.Button(button_frame, text="Supprimer", command=self.delete_item).pack(side='left', padx=5)
        ttk.Button(button_frame, text="Actualiser", command=self.refresh_data).pack(side='right', padx=5)
        
        # Frame pour la recherche
        search_frame = ttk.Frame(self)
        search_frame.pack(fill='x', padx=10, pady=5)
        
        ttk.Label(search_frame, text="Rechercher:").pack(side='left')
        self.search_var = tk.StringVar()
        self.search_var.trace('w', self.on_search)
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var)
        search_entry.pack(side='left', fill='x', expand=True, padx=5)
        
        # Treeview pour afficher les données
        self.tree = ttk.Treeview(self)
        self.tree.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Scrollbar pour le treeview
        scrollbar = ttk.Scrollbar(self, orient='vertical', command=self.tree.yview)
        scrollbar.pack(side='right', fill='y')
        self.tree.configure(yscrollcommand=scrollbar.set)
    
    def refresh_data(self):
        """Actualise les données"""
        try:
            self.data = self.model.get_all()
            self.populate_tree()
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du chargement: {str(e)}")
    
    def populate_tree(self):
        """Remplit le treeview avec les données"""
        # Effacer les données existantes
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        if not self.data:
            return
        
        # Configurer les colonnes
        columns = list(self.data[0].keys())
        self.tree['columns'] = columns
        self.tree['show'] = 'headings'
        
        # Configurer les en-têtes
        for col in columns:
            self.tree.heading(col, text=col.replace('_', ' ').title())
            self.tree.column(col, width=100)
        
        # Ajouter les données
        for item in self.data:
            values = [str(item.get(col, '')) for col in columns]
            self.tree.insert('', 'end', values=values)
    
    def on_search(self, *args):
        """Filtre les données selon la recherche"""
        search_term = self.search_var.get().lower()
        
        # Effacer les données existantes
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        if not self.data:
            return
        
        # Filtrer et afficher les données
        columns = list(self.data[0].keys())
        for item in self.data:
            # Vérifier si le terme de recherche est dans l'un des champs
            if any(search_term in str(item.get(col, '')).lower() for col in columns):
                values = [str(item.get(col, '')) for col in columns]
                self.tree.insert('', 'end', values=values)
    
    def get_selected_item(self):
        """Récupère l'élément sélectionné"""
        selection = self.tree.selection()
        if not selection:
            return None
        
        item = self.tree.item(selection[0])
        values = item['values']
        
        if not values or not self.data:
            return None
        
        # Trouver l'élément correspondant dans les données
        columns = list(self.data[0].keys())
        for data_item in self.data:
            if str(data_item.get(columns[0], '')) == str(values[0]):
                return data_item
        
        return None
    
    def add_item(self):
        """Ajoute un nouvel élément"""
        messagebox.showinfo("Info", "Fonctionnalité à implémenter")
    
    def edit_item(self):
        """Modifie l'élément sélectionné"""
        selected = self.get_selected_item()
        if not selected:
            messagebox.showwarning("Attention", "Veuillez sélectionner un élément à modifier")
            return
        messagebox.showinfo("Info", f"Modification de: {selected}")
    
    def delete_item(self):
        """Supprime l'élément sélectionné"""
        selected = self.get_selected_item()
        if not selected:
            messagebox.showwarning("Attention", "Veuillez sélectionner un élément à supprimer")
            return
        
        if messagebox.askyesno("Confirmation", "Êtes-vous sûr de vouloir supprimer cet élément?"):
            try:
                # Récupérer l'ID (première colonne)
                columns = list(selected.keys())
                id_value = selected[columns[0]]
                
                if self.model.delete(id_value):
                    messagebox.showinfo("Succès", "Élément supprimé avec succès")
                    self.refresh_data()
                else:
                    messagebox.showerror("Erreur", "Erreur lors de la suppression")
            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors de la suppression: {str(e)}")

class UniteFrame(BaseFrame):
    """Frame personnalisée pour les Unités, sans affichage de l'id secteur et autocomplétion du nom secteur"""

    def __init__(self, parent, title: str, model, secteur_model):
        self.secteur_model = secteur_model
        super().__init__(parent, title, model)

    def populate_tree(self):
        """Remplit le treeview avec les données, sans afficher id_secteur et sans redondance"""
        for item in self.tree.get_children():
            self.tree.delete(item)

        if not self.data:
            return

        # Exclure les colonnes redondantes et techniques
        excluded_columns = ['id_secteur']
        columns = [col for col in self.data[0].keys() if col not in excluded_columns]
        self.tree['columns'] = columns
        self.tree['show'] = 'headings'

        for col in columns:
            self.tree.heading(col, text=col.replace('_', ' ').title())
            self.tree.column(col, width=120)

        for item in self.data:
            values = [str(item.get(col, '')) for col in columns]
            self.tree.insert('', 'end', values=values)

    def add_item(self):
        """Ajoute une nouvelle unité avec autocomplétion du nom secteur"""
        def save():
            nom = entry_nom.get().strip()
            secteur_nom = entry_secteur.get().strip()
            if not nom or not secteur_nom:
                messagebox.showwarning("Attention", "Veuillez remplir tous les champs")
                return
            # Trouver l'id du secteur correspondant au nom
            secteurs = self.secteur_model.get_all()
            secteur_id = None
            for s in secteurs:
                if s.get('nom_secteur', '').lower() == secteur_nom.lower():
                    secteur_id = s.get('id_secteur')
                    break
            if not secteur_id:
                messagebox.showerror("Erreur", "Secteur non trouvé")
                return
            try:
                unite_id = self.model.create(nom, secteur_id)
                if unite_id:
                    messagebox.showinfo("Succès", "Unité ajoutée avec succès")
                top.destroy()
                self.refresh_data()
            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors de l'ajout: {str(e)}")

        top = tk.Toplevel(self)
        top.title("Ajouter une unité")
        tk.Label(top, text="Nom:").grid(row=0, column=0, padx=5, pady=5)
        entry_nom = ttk.Entry(top)
        entry_nom.grid(row=0, column=1, padx=5, pady=5)

        tk.Label(top, text="Nom Secteur:").grid(row=1, column=0, padx=5, pady=5)
        entry_secteur = ttk.Combobox(top)
        secteurs = self.secteur_model.get_all()
        entry_secteur['values'] = [s.get('nom_secteur', '') for s in secteurs]
        entry_secteur.grid(row=1, column=1, padx=5, pady=5)

        ttk.Button(top, text="Ajouter", command=save).grid(row=2, column=0, columnspan=2, pady=10)

    def edit_item(self):
        """Modifie l'unité sélectionnée"""
        selected = self.get_selected_item()
        if not selected:
            messagebox.showwarning("Attention", "Veuillez sélectionner une unité à modifier")
            return

        def save():
            nom = entry_nom.get().strip()
            secteur_nom = entry_secteur.get().strip()
            if not nom or not secteur_nom:
                messagebox.showwarning("Attention", "Veuillez remplir tous les champs")
                return

            # Trouver l'id du secteur correspondant au nom
            secteurs = self.secteur_model.get_all()
            secteur_id = None
            for s in secteurs:
                if s.get('nom_secteur', '').lower() == secteur_nom.lower():
                    secteur_id = s.get('id_secteur')
                    break

            if not secteur_id:
                messagebox.showerror("Erreur", "Secteur non trouvé")
                return

            try:
                success = self.model.update(selected['id_unite'], nom, secteur_id)
                if success:
                    messagebox.showinfo("Succès", "Unité modifiée avec succès")
                    top.destroy()
                    self.refresh_data()
                else:
                    messagebox.showerror("Erreur", "Erreur lors de la modification de l'unité")
            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors de la modification: {str(e)}")

        top = tk.Toplevel(self)
        top.title("Modifier une unité")
        top.geometry("400x200")

        tk.Label(top, text="Nom:").grid(row=0, column=0, padx=5, pady=5)
        entry_nom = ttk.Entry(top)
        entry_nom.insert(0, selected.get('nom_unite', ''))
        entry_nom.grid(row=0, column=1, padx=5, pady=5)

        tk.Label(top, text="Nom Secteur:").grid(row=1, column=0, padx=5, pady=5)
        entry_secteur = ttk.Combobox(top)
        secteurs = self.secteur_model.get_all()
        entry_secteur['values'] = [s.get('nom_secteur', '') for s in secteurs]
        entry_secteur.set(selected.get('nom_secteur', ''))
        entry_secteur.grid(row=1, column=1, padx=5, pady=5)

        ttk.Button(top, text="Modifier", command=save).grid(row=2, column=0, columnspan=2, pady=10)

    def delete_item(self):
        """Supprime l'unité sélectionnée"""
        selected = self.get_selected_item()
        if not selected:
            messagebox.showwarning("Attention", "Veuillez sélectionner une unité à supprimer")
            return

        if messagebox.askyesno("Confirmation",
                              f"Êtes-vous sûr de vouloir supprimer l'unité '{selected.get('nom_unite', '')}'?\n"
                              "Cette action supprimera également toutes les consommations associées."):
            try:
                success = self.model.delete(selected['id_unite'])
                if success:
                    messagebox.showinfo("Succès", "Unité supprimée avec succès")
                    self.refresh_data()
                else:
                    messagebox.showerror("Erreur", "Erreur lors de la suppression de l'unité")
            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors de la suppression: {str(e)}")

class SecteurFrame(BaseFrame):
    """Frame personnalisée pour les Secteurs avec boutons opérationnels"""

    def add_item(self):
        """Ajoute un nouveau secteur"""
        def save():
            nom = entry_nom.get().strip()
            if not nom:
                messagebox.showwarning("Attention", "Veuillez saisir un nom de secteur")
                return

            try:
                secteur_id = self.model.create(nom)
                if secteur_id:
                    messagebox.showinfo("Succès", "Secteur ajouté avec succès")
                    top.destroy()
                    self.refresh_data()
                else:
                    messagebox.showerror("Erreur", "Erreur lors de l'ajout du secteur")
            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors de l'ajout: {str(e)}")

        top = tk.Toplevel(self)
        top.title("Ajouter un secteur")
        top.geometry("400x150")

        tk.Label(top, text="Nom du secteur:").grid(row=0, column=0, padx=5, pady=5, sticky='e')
        entry_nom = ttk.Entry(top, width=30)
        entry_nom.grid(row=0, column=1, padx=5, pady=5)

        ttk.Button(top, text="Ajouter", command=save).grid(row=1, column=0, columnspan=2, pady=10)

    def edit_item(self):
        """Modifie le secteur sélectionné"""
        selected = self.get_selected_item()
        if not selected:
            messagebox.showwarning("Attention", "Veuillez sélectionner un secteur à modifier")
            return

        def save():
            nom = entry_nom.get().strip()
            if not nom:
                messagebox.showwarning("Attention", "Veuillez saisir un nom de secteur")
                return

            try:
                success = self.model.update(selected['id_secteur'], nom)
                if success:
                    messagebox.showinfo("Succès", "Secteur modifié avec succès")
                    top.destroy()
                    self.refresh_data()
                else:
                    messagebox.showerror("Erreur", "Erreur lors de la modification du secteur")
            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors de la modification: {str(e)}")

        top = tk.Toplevel(self)
        top.title("Modifier un secteur")
        top.geometry("400x150")

        tk.Label(top, text="Nom du secteur:").grid(row=0, column=0, padx=5, pady=5, sticky='e')
        entry_nom = ttk.Entry(top, width=30)
        entry_nom.insert(0, selected.get('nom_secteur', ''))
        entry_nom.grid(row=0, column=1, padx=5, pady=5)

        ttk.Button(top, text="Modifier", command=save).grid(row=1, column=0, columnspan=2, pady=10)

    def delete_item(self):
        """Supprime le secteur sélectionné"""
        selected = self.get_selected_item()
        if not selected:
            messagebox.showwarning("Attention", "Veuillez sélectionner un secteur à supprimer")
            return

        if messagebox.askyesno("Confirmation",
                              f"Êtes-vous sûr de vouloir supprimer le secteur '{selected.get('nom_secteur', '')}'?\n"
                              "Cette action supprimera également toutes les unités associées."):
            try:
                success = self.model.delete(selected['id_secteur'])
                if success:
                    messagebox.showinfo("Succès", "Secteur supprimé avec succès")
                    self.refresh_data()
                else:
                    messagebox.showerror("Erreur", "Erreur lors de la suppression du secteur")
            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors de la suppression: {str(e)}")

class PieceFrame(BaseFrame):
    """Frame personnalisée pour les Pièces, sans affichage de l'id catégorie et avec autocomplétion"""

    def __init__(self, parent, title: str, model, categorie_model):
        self.categorie_model = categorie_model
        super().__init__(parent, title, model)

    def populate_tree(self):
        """Remplit le treeview avec les données, sans afficher id_categorie"""
        for item in self.tree.get_children():
            self.tree.delete(item)

        if not self.data:
            return

        # Exclure la colonne 'id_categorie'
        columns = [col for col in self.data[0].keys() if col != 'id_categorie']
        self.tree['columns'] = columns
        self.tree['show'] = 'headings'

        for col in columns:
            self.tree.heading(col, text=col.replace('_', ' ').title())
            self.tree.column(col, width=120)

        for item in self.data:
            values = [str(item.get(col, '')) for col in columns]
            self.tree.insert('', 'end', values=values)

    def add_item(self):
        """Ajoute une nouvelle pièce avec autocomplétion de la catégorie"""
        def save():
            nom = entry_nom.get().strip()
            categorie_nom = entry_categorie.get().strip()
            if not nom or not categorie_nom:
                messagebox.showwarning("Attention", "Veuillez remplir tous les champs")
                return

            # Trouver l'id de la catégorie correspondant au nom
            categories = self.categorie_model.get_all()
            categorie_id = None
            for c in categories:
                if c.get('nom_categorie', '').lower() == categorie_nom.lower():
                    categorie_id = c.get('id_categorie')
                    break

            if not categorie_id:
                messagebox.showerror("Erreur", "Catégorie non trouvée")
                return

            try:
                # Utiliser la méthode create du modèle
                piece_id = self.model.create(nom, categorie_id)
                if piece_id:
                    messagebox.showinfo("Succès", "Pièce ajoutée avec succès")
                    top.destroy()
                    self.refresh_data()
                else:
                    messagebox.showerror("Erreur", "Erreur lors de l'ajout de la pièce")
            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors de l'ajout: {str(e)}")

        top = tk.Toplevel(self)
        top.title("Ajouter une pièce")
        top.geometry("400x200")

        tk.Label(top, text="Nom de la pièce:").grid(row=0, column=0, padx=5, pady=5, sticky='e')
        entry_nom = ttk.Entry(top, width=30)
        entry_nom.grid(row=0, column=1, padx=5, pady=5)

        tk.Label(top, text="Catégorie:").grid(row=1, column=0, padx=5, pady=5, sticky='e')
        entry_categorie = ttk.Combobox(top, width=27)
        categories = self.categorie_model.get_all()
        entry_categorie['values'] = [c.get('nom_categorie', '') for c in categories]
        entry_categorie.grid(row=1, column=1, padx=5, pady=5)

        ttk.Button(top, text="Ajouter", command=save).grid(row=2, column=0, columnspan=2, pady=10)

    def edit_item(self):
        """Modifie la pièce sélectionnée"""
        selected = self.get_selected_item()
        if not selected:
            messagebox.showwarning("Attention", "Veuillez sélectionner une pièce à modifier")
            return

        def save():
            nom = entry_nom.get().strip()
            categorie_nom = entry_categorie.get().strip()
            if not nom or not categorie_nom:
                messagebox.showwarning("Attention", "Veuillez remplir tous les champs")
                return

            # Trouver l'id de la catégorie correspondant au nom
            categories = self.categorie_model.get_all()
            categorie_id = None
            for c in categories:
                if c.get('nom_categorie', '').lower() == categorie_nom.lower():
                    categorie_id = c.get('id_categorie')
                    break

            if not categorie_id:
                messagebox.showerror("Erreur", "Catégorie non trouvée")
                return

            try:
                # Utiliser la méthode update du modèle
                success = self.model.update(selected['id_piece'], nom, categorie_id)
                if success:
                    messagebox.showinfo("Succès", "Pièce modifiée avec succès")
                    top.destroy()
                    self.refresh_data()
                else:
                    messagebox.showerror("Erreur", "Erreur lors de la modification de la pièce")
            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors de la modification: {str(e)}")

        top = tk.Toplevel(self)
        top.title("Modifier une pièce")
        top.geometry("400x200")

        tk.Label(top, text="Nom de la pièce:").grid(row=0, column=0, padx=5, pady=5, sticky='e')
        entry_nom = ttk.Entry(top, width=30)
        entry_nom.insert(0, selected.get('nom_piece', ''))
        entry_nom.grid(row=0, column=1, padx=5, pady=5)

        tk.Label(top, text="Catégorie:").grid(row=1, column=0, padx=5, pady=5, sticky='e')
        entry_categorie = ttk.Combobox(top, width=27)
        categories = self.categorie_model.get_all()
        entry_categorie['values'] = [c.get('nom_categorie', '') for c in categories]
        entry_categorie.set(selected.get('nom_categorie', ''))
        entry_categorie.grid(row=1, column=1, padx=5, pady=5)

        ttk.Button(top, text="Modifier", command=save).grid(row=2, column=0, columnspan=2, pady=10)

    def delete_item(self):
        """Supprime la pièce sélectionnée"""
        selected = self.get_selected_item()
        if not selected:
            messagebox.showwarning("Attention", "Veuillez sélectionner une pièce à supprimer")
            return

        if messagebox.askyesno("Confirmation",
                              f"Êtes-vous sûr de vouloir supprimer la pièce '{selected.get('nom_piece', '')}'?\n"
                              "Cette action supprimera également toutes les consommations associées."):
            try:
                success = self.model.delete(selected['id_piece'])
                if success:
                    messagebox.showinfo("Succès", "Pièce supprimée avec succès")
                    self.refresh_data()
                else:
                    messagebox.showerror("Erreur", "Erreur lors de la suppression de la pièce")
            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors de la suppression: {str(e)}")

class PeriodeFrame(BaseFrame):
    """Frame personnalisée pour les Périodes avec boutons opérationnels"""

    def add_item(self):
        """Ajoute une nouvelle période"""
        def save():
            try:
                date_str = entry_date.get().strip()
                mois = int(entry_mois.get())

                if not date_str or not (1 <= mois <= 12):
                    messagebox.showwarning("Attention", "Veuillez remplir correctement tous les champs")
                    return

                # Convertir la date (format YYYY-MM-DD)
                from datetime import datetime
                date_obj = datetime.strptime(date_str, "%Y-%m-%d").date()

                periode_id = self.model.create(date_obj, mois)
                if periode_id:
                    messagebox.showinfo("Succès", "Période ajoutée avec succès")
                    top.destroy()
                    self.refresh_data()
                else:
                    messagebox.showerror("Erreur", "Erreur lors de l'ajout de la période")
            except ValueError:
                messagebox.showerror("Erreur", "Format de date invalide (utilisez YYYY-MM-DD)")
            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors de l'ajout: {str(e)}")

        top = tk.Toplevel(self)
        top.title("Ajouter une période")
        top.geometry("400x200")

        tk.Label(top, text="Date (YYYY-MM-DD):").grid(row=0, column=0, padx=5, pady=5, sticky='e')
        entry_date = ttk.Entry(top, width=30)
        entry_date.grid(row=0, column=1, padx=5, pady=5)

        tk.Label(top, text="Mois (1-12):").grid(row=1, column=0, padx=5, pady=5, sticky='e')
        entry_mois = ttk.Combobox(top, width=27)
        entry_mois['values'] = list(range(1, 13))
        entry_mois.grid(row=1, column=1, padx=5, pady=5)

        ttk.Button(top, text="Ajouter", command=save).grid(row=2, column=0, columnspan=2, pady=10)

    def edit_item(self):
        """Modifie la période sélectionnée"""
        selected = self.get_selected_item()
        if not selected:
            messagebox.showwarning("Attention", "Veuillez sélectionner une période à modifier")
            return

        def save():
            try:
                date_str = entry_date.get().strip()
                mois = int(entry_mois.get())

                if not date_str or not (1 <= mois <= 12):
                    messagebox.showwarning("Attention", "Veuillez remplir correctement tous les champs")
                    return

                # Convertir la date
                from datetime import datetime
                date_obj = datetime.strptime(date_str, "%Y-%m-%d").date()

                success = self.model.update(selected['id_periode'], date_obj, mois)
                if success:
                    messagebox.showinfo("Succès", "Période modifiée avec succès")
                    top.destroy()
                    self.refresh_data()
                else:
                    messagebox.showerror("Erreur", "Erreur lors de la modification de la période")
            except ValueError:
                messagebox.showerror("Erreur", "Format de date invalide (utilisez YYYY-MM-DD)")
            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors de la modification: {str(e)}")

        top = tk.Toplevel(self)
        top.title("Modifier une période")
        top.geometry("400x200")

        tk.Label(top, text="Date (YYYY-MM-DD):").grid(row=0, column=0, padx=5, pady=5, sticky='e')
        entry_date = ttk.Entry(top, width=30)
        entry_date.insert(0, str(selected.get('date_periode', '')))
        entry_date.grid(row=0, column=1, padx=5, pady=5)

        tk.Label(top, text="Mois (1-12):").grid(row=1, column=0, padx=5, pady=5, sticky='e')
        entry_mois = ttk.Combobox(top, width=27)
        entry_mois['values'] = list(range(1, 13))
        entry_mois.set(selected.get('mois', ''))
        entry_mois.grid(row=1, column=1, padx=5, pady=5)

        ttk.Button(top, text="Modifier", command=save).grid(row=2, column=0, columnspan=2, pady=10)

    def delete_item(self):
        """Supprime la période sélectionnée"""
        selected = self.get_selected_item()
        if not selected:
            messagebox.showwarning("Attention", "Veuillez sélectionner une période à supprimer")
            return

        if messagebox.askyesno("Confirmation",
                              f"Êtes-vous sûr de vouloir supprimer cette période?\n"
                              "Cette action supprimera également toutes les consommations associées."):
            try:
                success = self.model.delete(selected['id_periode'])
                if success:
                    messagebox.showinfo("Succès", "Période supprimée avec succès")
                    self.refresh_data()
                else:
                    messagebox.showerror("Erreur", "Erreur lors de la suppression de la période")
            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors de la suppression: {str(e)}")

class ConsommationFrame(BaseFrame):
    """Frame personnalisée pour les Consommations avec formulaires avancés"""

    def __init__(self, parent, title: str, model, unite_model, piece_model, periode_model):
        self.unite_model = unite_model
        self.piece_model = piece_model
        self.periode_model = periode_model
        super().__init__(parent, title, model)

    def init_ui(self):
        """Configure l'interface utilisateur avec boutons d'export"""
        # Titre
        title_label = ttk.Label(self, text=f"Gestion des {self.title}",
                               font=('Arial', 14, 'bold'))
        title_label.pack(pady=10)

        # Frame pour les boutons principaux
        button_frame = ttk.Frame(self)
        button_frame.pack(fill='x', padx=10, pady=5)

        # Boutons standards
        ttk.Button(button_frame, text="Ajouter", command=self.add_item).pack(side='left', padx=5)
        ttk.Button(button_frame, text="Modifier", command=self.edit_item).pack(side='left', padx=5)
        ttk.Button(button_frame, text="Supprimer", command=self.delete_item).pack(side='left', padx=5)

        # Séparateur
        ttk.Separator(button_frame, orient='vertical').pack(side='left', padx=10, fill='y')

        # Boutons d'export
        ttk.Button(button_frame, text="🌐 Export HTML", command=self.export_html).pack(side='left', padx=5)
        ttk.Button(button_frame, text="📈 Export Excel", command=self.export_excel_table).pack(side='left', padx=5)
        ttk.Button(button_frame, text="📄 Export Word", command=self.export_word_table).pack(side='left', padx=5)
        ttk.Button(button_frame, text="⚙️ Export Personnalisé", command=self.export_custom).pack(side='left', padx=5)

        # Bouton actualiser à droite
        ttk.Button(button_frame, text="Actualiser", command=self.refresh_data).pack(side='right', padx=5)

        # Frame pour la recherche
        search_frame = ttk.Frame(self)
        search_frame.pack(fill='x', padx=10, pady=5)

        ttk.Label(search_frame, text="Rechercher:").pack(side='left')
        self.search_var = tk.StringVar()
        self.search_var.trace('w', self.on_search)
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var)
        search_entry.pack(side='left', fill='x', expand=True, padx=5)

        # Treeview avec scrollbars
        tree_frame = ttk.Frame(self)
        tree_frame.pack(fill='both', expand=True, padx=10, pady=5)

        self.tree = ttk.Treeview(tree_frame)

        # Scrollbars
        v_scrollbar = ttk.Scrollbar(tree_frame, orient='vertical', command=self.tree.yview)
        h_scrollbar = ttk.Scrollbar(tree_frame, orient='horizontal', command=self.tree.xview)
        self.tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # Placement
        self.tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')

        tree_frame.grid_rowconfigure(0, weight=1)
        tree_frame.grid_columnconfigure(0, weight=1)

    def populate_tree(self):
        """Remplit le treeview avec les données dans l'ordre spécifié"""
        for item in self.tree.get_children():
            self.tree.delete(item)

        if not self.data:
            return

        # Ordre d'affichage spécifié : secteur, unité, pièce, catégorie, date, attribué, consommé, reste
        ordered_columns = [
            'nom_secteur', 'nom_unite', 'nom_piece', 'nom_categorie', 'date_periode',
            'nombre_attribue', 'nombre_consomme', 'reste_non_consomme'
        ]

        # Titres d'affichage correspondants
        column_titles = {
            'nom_secteur': 'Secteur',
            'nom_unite': 'Unité',
            'nom_piece': 'Pièce',
            'nom_categorie': 'Catégorie',
            'date_periode': 'Date',
            'nombre_attribue': 'Nombre Attribué',
            'nombre_consomme': 'Nombre Consommé',
            'reste_non_consomme': 'Reste Non Consommé'
        }

        # Largeurs de colonnes optimisées
        column_widths = {
            'nom_secteur': 120,
            'nom_unite': 120,
            'nom_piece': 180,
            'nom_categorie': 100,
            'date_periode': 100,
            'nombre_attribue': 110,
            'nombre_consomme': 110,
            'reste_non_consomme': 120
        }

        self.tree['columns'] = ordered_columns
        self.tree['show'] = 'headings'

        for col in ordered_columns:
            title = column_titles.get(col, col.replace('_', ' ').title())
            width = column_widths.get(col, 120)
            self.tree.heading(col, text=title)
            self.tree.column(col, width=width)

        for item in self.data:
            values = [str(item.get(col, '')) for col in ordered_columns]
            self.tree.insert('', 'end', values=values)

    def add_item(self):
        """Ajoute une nouvelle consommation avec formulaire avancé"""
        def save():
            try:
                # Récupérer les valeurs
                unite_nom = entry_unite.get().strip()
                piece_nom = entry_piece.get().strip()
                periode_text = entry_periode.get().strip()
                nombre_attribue = int(entry_attribue.get() or 0)
                nombre_consomme = int(entry_consomme.get() or 0)

                if not unite_nom or not piece_nom or not periode_text:
                    messagebox.showwarning("Attention", "Veuillez remplir tous les champs obligatoires")
                    return

                # Trouver l'ID de l'unité
                unites = self.unite_model.get_all()
                unite_id = None
                for u in unites:
                    if u.get('nom_unite', '').lower() == unite_nom.lower():
                        unite_id = u.get('id_unite')
                        break

                if not unite_id:
                    messagebox.showerror("Erreur", "Unité non trouvée")
                    return

                # Trouver l'ID de la pièce
                pieces = self.piece_model.get_all()
                piece_id = None
                for p in pieces:
                    if p.get('nom_piece', '').lower() == piece_nom.lower():
                        piece_id = p.get('id_piece')
                        break

                if not piece_id:
                    messagebox.showerror("Erreur", "Pièce non trouvée")
                    return

                # Trouver l'ID de la période sélectionnée
                periode_id = periode_map.get(periode_text)
                if not periode_id:
                    messagebox.showerror("Erreur", "Période non trouvée")
                    return

                # Créer la consommation
                consommation_id = self.model.create(unite_id, piece_id, periode_id, nombre_attribue, nombre_consomme)
                if consommation_id:
                    messagebox.showinfo("Succès", "Consommation ajoutée avec succès")
                    top.destroy()
                    self.refresh_data()
                else:
                    messagebox.showerror("Erreur", "Erreur lors de l'ajout de la consommation")

            except ValueError as ve:
                messagebox.showerror("Erreur", f"Erreur de format: {str(ve)}")
            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors de l'ajout: {str(e)}")

        top = tk.Toplevel(self)
        top.title("Ajouter une consommation")
        top.geometry("500x400")

        # Unité
        tk.Label(top, text="Unité:").grid(row=0, column=0, padx=5, pady=5, sticky='e')
        entry_unite = ttk.Combobox(top, width=30)
        unites = self.unite_model.get_all()
        entry_unite['values'] = [u.get('nom_unite', '') for u in unites]
        entry_unite.grid(row=0, column=1, padx=5, pady=5)

        # Pièce
        tk.Label(top, text="Pièce:").grid(row=1, column=0, padx=5, pady=5, sticky='e')
        entry_piece = ttk.Combobox(top, width=30)
        pieces = self.piece_model.get_all()
        entry_piece['values'] = [p.get('nom_piece', '') for p in pieces]
        entry_piece.grid(row=1, column=1, padx=5, pady=5)

        # Période (sélection parmi les périodes existantes)
        tk.Label(top, text="Période:").grid(row=2, column=0, padx=5, pady=5, sticky='e')
        entry_periode = ttk.Combobox(top, width=30)
        periodes = self.periode_model.get_all()
        periode_options = []
        periode_map = {}
        for per in periodes:
            display_text = f"{per.get('date_periode')} (Mois {per.get('mois')})"
            periode_options.append(display_text)
            periode_map[display_text] = per.get('id_periode')
        entry_periode['values'] = periode_options
        entry_periode.grid(row=2, column=1, padx=5, pady=5)

        # Nombre attribué
        tk.Label(top, text="Nombre attribué:").grid(row=3, column=0, padx=5, pady=5, sticky='e')
        entry_attribue = ttk.Entry(top, width=32)
        entry_attribue.insert(0, "0")
        entry_attribue.grid(row=3, column=1, padx=5, pady=5)

        # Nombre consommé
        tk.Label(top, text="Nombre consommé:").grid(row=4, column=0, padx=5, pady=5, sticky='e')
        entry_consomme = ttk.Entry(top, width=32)
        entry_consomme.insert(0, "0")
        entry_consomme.grid(row=4, column=1, padx=5, pady=5)

        # Calcul automatique du reste
        tk.Label(top, text="Reste (calculé auto):").grid(row=5, column=0, padx=5, pady=5, sticky='e')
        label_reste = tk.Label(top, text="0", fg="blue", font=("Arial", 10, "bold"))
        label_reste.grid(row=5, column=1, padx=5, pady=5, sticky='w')

        def update_reste(*args):
            try:
                attribue = int(entry_attribue.get() or 0)
                consomme = int(entry_consomme.get() or 0)
                reste = attribue - consomme
                label_reste.config(text=str(reste))
                if reste < 0:
                    label_reste.config(fg="red")
                elif reste == 0:
                    label_reste.config(fg="green")
                else:
                    label_reste.config(fg="blue")
            except ValueError:
                label_reste.config(text="?", fg="gray")

        # Lier les événements pour le calcul automatique
        entry_attribue.bind('<KeyRelease>', update_reste)
        entry_consomme.bind('<KeyRelease>', update_reste)

        ttk.Button(top, text="Ajouter", command=save).grid(row=6, column=0, columnspan=2, pady=20)

    def edit_item(self):
        """Modifie la consommation sélectionnée"""
        selected = self.get_selected_item()
        if not selected:
            messagebox.showwarning("Attention", "Veuillez sélectionner une consommation à modifier")
            return

        # Trouver l'ID de la consommation dans les données originales
        consommation_id = None
        consommation_data = None
        for data_item in self.data:
            if (data_item.get('nom_unite') == selected.get('nom_unite') and
                data_item.get('nom_piece') == selected.get('nom_piece') and
                str(data_item.get('date_periode')) == str(selected.get('date_periode'))):
                consommation_id = data_item.get('id_consommation')
                consommation_data = data_item
                break

        if not consommation_id:
            messagebox.showerror("Erreur", "Impossible de trouver l'ID de la consommation")
            return

        def save():
            try:
                # Récupérer les valeurs
                unite_nom = entry_unite.get().strip()
                piece_nom = entry_piece.get().strip()
                periode_text = entry_periode.get().strip()
                nombre_attribue = int(entry_attribue.get() or 0)
                nombre_consomme = int(entry_consomme.get() or 0)

                if not unite_nom or not piece_nom or not periode_text:
                    messagebox.showwarning("Attention", "Veuillez remplir tous les champs obligatoires")
                    return

                # Trouver l'ID de l'unité
                unites = self.unite_model.get_all()
                unite_id = None
                for u in unites:
                    if u.get('nom_unite', '').lower() == unite_nom.lower():
                        unite_id = u.get('id_unite')
                        break

                if not unite_id:
                    messagebox.showerror("Erreur", "Unité non trouvée")
                    return

                # Trouver l'ID de la pièce
                pieces = self.piece_model.get_all()
                piece_id = None
                for p in pieces:
                    if p.get('nom_piece', '').lower() == piece_nom.lower():
                        piece_id = p.get('id_piece')
                        break

                if not piece_id:
                    messagebox.showerror("Erreur", "Pièce non trouvée")
                    return

                # Trouver l'ID de la période sélectionnée
                periode_id = periode_map.get(periode_text)
                if not periode_id:
                    messagebox.showerror("Erreur", "Période non trouvée")
                    return

                # Modifier la consommation
                success = self.model.update(consommation_id, unite_id, piece_id, periode_id, nombre_attribue, nombre_consomme)
                if success:
                    messagebox.showinfo("Succès", "Consommation modifiée avec succès")
                    top.destroy()
                    self.refresh_data()
                else:
                    messagebox.showerror("Erreur", "Erreur lors de la modification de la consommation")

            except ValueError as ve:
                messagebox.showerror("Erreur", f"Erreur de format: {str(ve)}")
            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors de la modification: {str(e)}")

        top = tk.Toplevel(self)
        top.title("Modifier une consommation")
        top.geometry("500x400")

        # Unité
        tk.Label(top, text="Unité:").grid(row=0, column=0, padx=5, pady=5, sticky='e')
        entry_unite = ttk.Combobox(top, width=30)
        unites = self.unite_model.get_all()
        entry_unite['values'] = [u.get('nom_unite', '') for u in unites]
        entry_unite.set(selected.get('nom_unite', ''))
        entry_unite.grid(row=0, column=1, padx=5, pady=5)

        # Pièce
        tk.Label(top, text="Pièce:").grid(row=1, column=0, padx=5, pady=5, sticky='e')
        entry_piece = ttk.Combobox(top, width=30)
        pieces = self.piece_model.get_all()
        entry_piece['values'] = [p.get('nom_piece', '') for p in pieces]
        entry_piece.set(selected.get('nom_piece', ''))
        entry_piece.grid(row=1, column=1, padx=5, pady=5)

        # Période (sélection parmi les périodes existantes)
        tk.Label(top, text="Période:").grid(row=2, column=0, padx=5, pady=5, sticky='e')
        entry_periode = ttk.Combobox(top, width=30)
        periodes = self.periode_model.get_all()
        periode_options = []
        periode_map = {}
        for per in periodes:
            display_text = f"{per.get('date_periode')} (Mois {per.get('mois')})"
            periode_options.append(display_text)
            periode_map[display_text] = per.get('id_periode')
        entry_periode['values'] = periode_options
        # Pré-sélectionner la période actuelle
        current_periode_text = f"{selected.get('date_periode')} (Mois {selected.get('mois')})"
        entry_periode.set(current_periode_text)
        entry_periode.grid(row=2, column=1, padx=5, pady=5)

        # Nombre attribué
        tk.Label(top, text="Nombre attribué:").grid(row=3, column=0, padx=5, pady=5, sticky='e')
        entry_attribue = ttk.Entry(top, width=32)
        entry_attribue.insert(0, str(selected.get('nombre_attribue', '0')))
        entry_attribue.grid(row=3, column=1, padx=5, pady=5)

        # Nombre consommé
        tk.Label(top, text="Nombre consommé:").grid(row=4, column=0, padx=5, pady=5, sticky='e')
        entry_consomme = ttk.Entry(top, width=32)
        entry_consomme.insert(0, str(selected.get('nombre_consomme', '0')))
        entry_consomme.grid(row=4, column=1, padx=5, pady=5)

        # Calcul automatique du reste
        tk.Label(top, text="Reste (calculé auto):").grid(row=5, column=0, padx=5, pady=5, sticky='e')
        label_reste = tk.Label(top, text=str(selected.get('reste_non_consomme', '0')), fg="blue", font=("Arial", 10, "bold"))
        label_reste.grid(row=5, column=1, padx=5, pady=5, sticky='w')

        def update_reste(*args):
            try:
                attribue = int(entry_attribue.get() or 0)
                consomme = int(entry_consomme.get() or 0)
                reste = attribue - consomme
                label_reste.config(text=str(reste))
                if reste < 0:
                    label_reste.config(fg="red")
                elif reste == 0:
                    label_reste.config(fg="green")
                else:
                    label_reste.config(fg="blue")
            except ValueError:
                label_reste.config(text="?", fg="gray")

        # Lier les événements pour le calcul automatique
        entry_attribue.bind('<KeyRelease>', update_reste)
        entry_consomme.bind('<KeyRelease>', update_reste)

        ttk.Button(top, text="Modifier", command=save).grid(row=6, column=0, columnspan=2, pady=20)

    def delete_item(self):
        """Supprime la consommation sélectionnée"""
        selected = self.get_selected_item()
        if not selected:
            messagebox.showwarning("Attention", "Veuillez sélectionner une consommation à supprimer")
            return

        if messagebox.askyesno("Confirmation",
                              "Êtes-vous sûr de vouloir supprimer cette consommation?"):
            try:
                # Trouver l'ID de la consommation dans les données originales
                consommation_id = None
                for data_item in self.data:
                    # Comparer plusieurs champs pour identifier la ligne
                    if (data_item.get('nom_unite') == selected.get('nom_unite') and
                        data_item.get('nom_piece') == selected.get('nom_piece') and
                        str(data_item.get('date_periode')) == str(selected.get('date_periode'))):
                        consommation_id = data_item.get('id_consommation')
                        break

                if consommation_id:
                    success = self.model.delete(consommation_id)
                    if success:
                        messagebox.showinfo("Succès", "Consommation supprimée avec succès")
                        self.refresh_data()
                    else:
                        messagebox.showerror("Erreur", "Erreur lors de la suppression de la consommation")
                else:
                    messagebox.showerror("Erreur", "Impossible de trouver l'ID de la consommation")
            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors de la suppression: {str(e)}")

    def export_html(self):
        """Exporte les données de consommation en HTML formaté"""
        if not self.data:
            messagebox.showwarning("Attention", "Aucune donnée à exporter")
            return

        filename = filedialog.asksaveasfilename(
            defaultextension=".html",
            filetypes=[("HTML files", "*.html"), ("Web files", "*.htm"), ("All files", "*.*")],
            title="Sauvegarder le fichier HTML"
        )

        if filename:
            try:
                # Créer un fichier HTML avec tableau formaté
                html_content = self._create_html_table()

                with open(filename, 'w', encoding='utf-8') as htmlfile:
                    htmlfile.write(html_content)

                messagebox.showinfo("Succès", f"Données exportées vers {filename}\n(Format HTML - Ouvrir avec navigateur, Excel ou Word)")
            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors de l'export: {str(e)}")

    def export_excel_table(self):
        """Exporte les données en format Excel compatible (.xls)"""
        if not self.data:
            messagebox.showwarning("Attention", "Aucune donnée à exporter")
            return

        filename = filedialog.asksaveasfilename(
            defaultextension=".xls",
            filetypes=[("Excel files", "*.xls"), ("Excel Template", "*.xltx"), ("All files", "*.*")],
            title="Sauvegarder le fichier Excel"
        )

        if filename:
            try:
                # Créer un fichier HTML que Excel peut ouvrir parfaitement
                excel_html = self._create_excel_html()

                with open(filename, 'w', encoding='utf-8') as file:
                    file.write(excel_html)

                messagebox.showinfo("Succès", f"Fichier Excel exporté vers {filename}\n(Format compatible Excel - Ouvrir avec Excel)")
            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors de l'export: {str(e)}")

    def _create_excel_html(self):
        """Crée un fichier HTML optimisé pour Excel"""
        fieldnames = ['nom_secteur', 'nom_unite', 'nom_piece', 'nom_categorie', 'date_periode',
                     'nombre_attribue', 'nombre_consomme', 'reste_non_consomme']

        headers = {
            'nom_secteur': 'Secteur',
            'nom_unite': 'Unité',
            'nom_piece': 'Pièce',
            'nom_categorie': 'Catégorie',
            'date_periode': 'Date',
            'nombre_attribue': 'Nombre Attribué',
            'nombre_consomme': 'Nombre Consommé',
            'reste_non_consomme': 'Reste Non Consommé'
        }

        # HTML optimisé pour Excel
        html = """<html xmlns:o="urn:schemas-microsoft-com:office:office"
xmlns:x="urn:schemas-microsoft-com:office:excel"
xmlns="http://www.w3.org/TR/REC-html40">
<head>
<meta charset="UTF-8">
<meta name="ProgId" content="Excel.Sheet">
<meta name="Generator" content="Gestion MUN">
<!--[if gte mso 9]><xml>
<x:ExcelWorkbook>
<x:ExcelWorksheets>
<x:ExcelWorksheet>
<x:Name>Consommations</x:Name>
<x:WorksheetOptions>
<x:Selected/>
<x:ProtectContents>False</x:ProtectContents>
<x:ProtectObjects>False</x:ProtectObjects>
<x:ProtectScenarios>False</x:ProtectScenarios>
</x:WorksheetOptions>
</x:ExcelWorksheet>
</x:ExcelWorksheets>
</x:ExcelWorkbook>
</xml><![endif]-->
<style>
.header { background-color: #4472C4; color: white; font-weight: bold; text-align: center; }
.data { border: 1px solid #000000; }
.number { text-align: right; }
table { border-collapse: collapse; }
</style>
</head>
<body>
<table border="1">
<tr>"""

        # En-têtes
        for field in fieldnames:
            html += f'\n<th class="header">{headers[field]}</th>'

        html += '\n</tr>'

        # Données
        for item in self.data:
            html += '\n<tr>'
            for field in fieldnames:
                value = str(item.get(field, ''))
                css_class = "data"
                if field in ['nombre_attribue', 'nombre_consomme', 'reste_non_consomme']:
                    css_class += " number"

                html += f'\n<td class="{css_class}">{value}</td>'
            html += '\n</tr>'

        html += """
</table>
</body>
</html>"""

        return html

    def _create_excel_xml_old(self):
        """Crée un fichier XML compatible Excel"""
        fieldnames = ['nom_secteur', 'nom_unite', 'nom_piece', 'nom_categorie', 'date_periode',
                     'nombre_attribue', 'nombre_consomme', 'reste_non_consomme']

        headers = {
            'nom_secteur': 'Secteur',
            'nom_unite': 'Unité',
            'nom_piece': 'Pièce',
            'nom_categorie': 'Catégorie',
            'date_periode': 'Date',
            'nombre_attribue': 'Nombre Attribué',
            'nombre_consomme': 'Nombre Consommé',
            'reste_non_consomme': 'Reste Non Consommé'
        }

        # XML Excel Spreadsheet format
        xml = '''<?xml version="1.0" encoding="UTF-8"?>
<?mso-application progid="Excel.Sheet"?>
<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet"
 xmlns:o="urn:schemas-microsoft-com:office:office"
 xmlns:x="urn:schemas-microsoft-com:office:excel"
 xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet"
 xmlns:html="http://www.w3.org/TR/REC-html40">
 <DocumentProperties xmlns="urn:schemas-microsoft-com:office:office">
  <Title>Export Consommations - Gestion MUN</Title>
  <Author>Application Gestion MUN</Author>
  <Created>''' + datetime.now().strftime("%Y-%m-%dT%H:%M:%S") + '''</Created>
 </DocumentProperties>
 <ExcelWorkbook xmlns="urn:schemas-microsoft-com:office:excel">
  <WindowHeight>12000</WindowHeight>
  <WindowWidth>18000</WindowWidth>
  <WindowTopX>0</WindowTopX>
  <WindowTopY>0</WindowTopY>
  <ProtectStructure>False</ProtectStructure>
  <ProtectWindows>False</ProtectWindows>
 </ExcelWorkbook>
 <Styles>
  <Style ss:ID="Default" ss:Name="Normal">
   <Alignment ss:Vertical="Bottom"/>
   <Borders/>
   <Font ss:FontName="Calibri" x:Family="Swiss" ss:Size="11" ss:Color="#000000"/>
   <Interior/>
   <NumberFormat/>
   <Protection/>
  </Style>
  <Style ss:ID="s62">
   <Alignment ss:Horizontal="Center" ss:Vertical="Bottom"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="Calibri" x:Family="Swiss" ss:Size="11" ss:Color="#FFFFFF" ss:Bold="1"/>
   <Interior ss:Color="#4472C4" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="s63">
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="Calibri" x:Family="Swiss" ss:Size="11"/>
  </Style>
 </Styles>
 <Worksheet ss:Name="Consommations">
  <Table ss:ExpandedColumnCount="''' + str(len(fieldnames)) + '''" ss:ExpandedRowCount="''' + str(len(self.data) + 1) + '''" x:FullColumns="1" x:FullRows="1">'''

        # Définir les largeurs de colonnes
        col_widths = [120, 120, 180, 120, 100, 120, 120, 140]
        for i, width in enumerate(col_widths):
            xml += f'\n   <Column ss:Index="{i+1}" ss:AutoFitWidth="0" ss:Width="{width}"/>'

        # En-têtes
        xml += '\n   <Row ss:AutoFitHeight="0" ss:Height="20">'
        for field in fieldnames:
            xml += f'\n    <Cell ss:StyleID="s62"><Data ss:Type="String">{headers[field]}</Data></Cell>'
        xml += '\n   </Row>'

        # Données
        for item in self.data:
            xml += '\n   <Row>'
            for field in fieldnames:
                value = str(item.get(field, ''))
                # Échapper les caractères XML
                value = value.replace('&', '&amp;').replace('<', '&lt;').replace('>', '&gt;')

                # Déterminer le type de données
                if field in ['nombre_attribue', 'nombre_consomme', 'reste_non_consomme']:
                    data_type = "Number"
                else:
                    data_type = "String"

                xml += f'\n    <Cell ss:StyleID="s63"><Data ss:Type="{data_type}">{value}</Data></Cell>'
            xml += '\n   </Row>'

        xml += '''
  </Table>
  <WorksheetOptions xmlns="urn:schemas-microsoft-com:office:excel">
   <PageSetup>
    <Header x:Margin="0.3"/>
    <Footer x:Margin="0.3"/>
    <PageMargins x:Bottom="0.75" x:Left="0.7" x:Right="0.7" x:Top="0.75"/>
   </PageSetup>
   <Selected/>
   <Panes>
    <Pane>
     <Number>3</Number>
     <ActiveRow>1</ActiveRow>
     <ActiveCol>0</ActiveCol>
    </Pane>
   </Panes>
   <ProtectObjects>False</ProtectObjects>
   <ProtectScenarios>False</ProtectScenarios>
  </WorksheetOptions>
 </Worksheet>
</Workbook>'''

        return xml

    def _create_html_table(self):
        """Crée un tableau HTML formaté avec les données de consommation"""
        fieldnames = ['nom_secteur', 'nom_unite', 'nom_piece', 'nom_categorie', 'date_periode',
                     'nombre_attribue', 'nombre_consomme', 'reste_non_consomme']

        headers = {
            'nom_secteur': 'Secteur',
            'nom_unite': 'Unité',
            'nom_piece': 'Pièce',
            'nom_categorie': 'Catégorie',
            'date_periode': 'Date',
            'nombre_attribue': 'Nombre Attribué',
            'nombre_consomme': 'Nombre Consommé',
            'reste_non_consomme': 'Reste Non Consommé'
        }

        html = """<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Export Consommations - Gestion MUN</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1 { color: #2c3e50; text-align: center; }
        table { border-collapse: collapse; width: 100%; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #3498db; color: white; font-weight: bold; }
        tr:nth-child(even) { background-color: #f2f2f2; }
        tr:hover { background-color: #e8f4fd; }
        .number { text-align: right; }
        .date { text-align: center; }
        .footer { margin-top: 20px; font-size: 12px; color: #666; text-align: center; }
    </style>
</head>
<body>
    <h1>📊 Export des Consommations - Gestion MUN</h1>
    <p><strong>Date d'export :</strong> """ + datetime.now().strftime("%d/%m/%Y à %H:%M") + """</p>
    <p><strong>Nombre d'enregistrements :</strong> """ + str(len(self.data)) + """</p>

    <table>
        <thead>
            <tr>"""

        # En-têtes
        for field in fieldnames:
            html += f"\n                <th>{headers[field]}</th>"

        html += """
            </tr>
        </thead>
        <tbody>"""

        # Données
        for item in self.data:
            html += "\n            <tr>"
            for field in fieldnames:
                value = str(item.get(field, ''))
                css_class = ""

                # Appliquer des classes CSS selon le type de données
                if field in ['nombre_attribue', 'nombre_consomme', 'reste_non_consomme']:
                    css_class = ' class="number"'
                elif field == 'date_periode':
                    css_class = ' class="date"'

                html += f'\n                <td{css_class}>{value}</td>'
            html += "\n            </tr>"

        html += """
        </tbody>
    </table>

    <div class="footer">
        <p>📋 Export généré par l'application Gestion MUN</p>
        <p>💡 Ce fichier peut être ouvert dans Excel, Word, ou tout navigateur web</p>
    </div>
</body>
</html>"""

        return html

    def export_word_table(self):
        """Exporte les données en format Word compatible (.doc)"""
        if not self.data:
            messagebox.showwarning("Attention", "Aucune donnée à exporter")
            return

        filename = filedialog.asksaveasfilename(
            defaultextension=".doc",
            filetypes=[("Word files", "*.doc"), ("Word Template", "*.dotx"), ("All files", "*.*")],
            title="Sauvegarder le fichier Word"
        )

        if filename:
            try:
                # Créer un fichier HTML que Word peut ouvrir parfaitement
                word_html = self._create_word_html()

                with open(filename, 'w', encoding='utf-8') as file:
                    file.write(word_html)

                messagebox.showinfo("Succès", f"Fichier Word exporté vers {filename}\n(Format compatible Word - Ouvrir avec Word)")
            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors de l'export: {str(e)}")

    def _create_word_html(self):
        """Crée un fichier HTML optimisé pour Word"""
        fieldnames = ['nom_secteur', 'nom_unite', 'nom_piece', 'nom_categorie', 'date_periode',
                     'nombre_attribue', 'nombre_consomme', 'reste_non_consomme']

        headers = {
            'nom_secteur': 'Secteur',
            'nom_unite': 'Unité',
            'nom_piece': 'Pièce',
            'nom_categorie': 'Catégorie',
            'date_periode': 'Date',
            'nombre_attribue': 'Nombre Attribué',
            'nombre_consomme': 'Nombre Consommé',
            'reste_non_consomme': 'Reste Non Consommé'
        }

        # HTML optimisé pour Word
        html = """<html xmlns:o="urn:schemas-microsoft-com:office:office"
xmlns:w="urn:schemas-microsoft-com:office:word"
xmlns="http://www.w3.org/TR/REC-html40">
<head>
<meta charset="UTF-8">
<meta name="ProgId" content="Word.Document">
<meta name="Generator" content="Gestion MUN">
<!--[if gte mso 9]><xml>
<w:WordDocument>
<w:View>Print</w:View>
<w:Zoom>100</w:Zoom>
<w:DoNotPromptForConvert/>
<w:DoNotShowRevisions/>
<w:DoNotPrintRevisions/>
<w:DisplayHorizontalDrawingGridEvery>0</w:DisplayHorizontalDrawingGridEvery>
<w:DisplayVerticalDrawingGridEvery>2</w:DisplayVerticalDrawingGridEvery>
<w:UseMarginsForDrawingGridOrigin/>
<w:ValidateAgainstSchemas/>
<w:SaveIfXMLInvalid>false</w:SaveIfXMLInvalid>
<w:IgnoreMixedContent>false</w:IgnoreMixedContent>
<w:AlwaysShowPlaceholderText>false</w:AlwaysShowPlaceholderText>
<w:Compatibility>
<w:BreakWrappedTables/>
<w:SnapToGridInCell/>
<w:WrapTextWithPunct/>
<w:UseAsianBreakRules/>
</w:Compatibility>
</w:WordDocument>
</xml><![endif]-->
<style>
body { font-family: Calibri, sans-serif; margin: 20px; }
h1 { color: #2c3e50; text-align: center; font-size: 18pt; }
.info { margin: 10px 0; font-weight: bold; }
table { border-collapse: collapse; width: 100%; margin: 20px 0; }
.header { background-color: #4472C4; color: white; font-weight: bold; text-align: center; padding: 8px; border: 1px solid #000; }
.data { padding: 6px; border: 1px solid #000; }
.alt { background-color: #F2F2F2; }
.number { text-align: right; }
</style>
</head>
<body>
<h1>Export des Consommations - Gestion MUN</h1>
<p class="info">Date d'export : """ + datetime.now().strftime("%d/%m/%Y à %H:%M") + """</p>
<p class="info">Nombre d'enregistrements : """ + str(len(self.data)) + """</p>

<table>
<tr>"""

        # En-têtes
        for field in fieldnames:
            html += f'\n<th class="header">{headers[field]}</th>'

        html += '\n</tr>'

        # Données avec alternance de couleurs
        for i, item in enumerate(self.data):
            alt_class = " alt" if i % 2 == 0 else ""
            html += '\n<tr>'
            for field in fieldnames:
                value = str(item.get(field, ''))
                css_class = f"data{alt_class}"
                if field in ['nombre_attribue', 'nombre_consomme', 'reste_non_consomme']:
                    css_class += " number"

                html += f'\n<td class="{css_class}">{value}</td>'
            html += '\n</tr>'

        html += """
</table>

<p><i>Document généré par l'application Gestion MUN</i></p>
</body>
</html>"""

        return html

    def _create_word_xml_old(self):
        """Crée un fichier XML compatible Word"""
        fieldnames = ['nom_secteur', 'nom_unite', 'nom_piece', 'nom_categorie', 'date_periode',
                     'nombre_attribue', 'nombre_consomme', 'reste_non_consomme']

        headers = {
            'nom_secteur': 'Secteur',
            'nom_unite': 'Unité',
            'nom_piece': 'Pièce',
            'nom_categorie': 'Catégorie',
            'date_periode': 'Date',
            'nombre_attribue': 'Nombre Attribué',
            'nombre_consomme': 'Nombre Consommé',
            'reste_non_consomme': 'Reste Non Consommé'
        }

        # XML Word Document format
        xml = '''<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<?mso-application progid="Word.Document"?>
<w:wordDocument xmlns:w="http://schemas.microsoft.com/office/word/2003/wordml"
 xmlns:v="urn:schemas-microsoft-com:vml"
 xmlns:w10="urn:schemas-microsoft-com:office:word"
 xmlns:sl="http://schemas.microsoft.com/schemaLibrary/2003/core"
 xmlns:aml="http://schemas.microsoft.com/aml/2001/core"
 xmlns:wx="http://schemas.microsoft.com/office/word/2003/auxHint"
 xmlns:o="urn:schemas-microsoft-com:office:office"
 xmlns:dt="uuid:C2F41010-65B3-11d1-A29F-00AA00C14882"
 w:macrosPresent="no" w:embeddedObjPresent="no" w:ocxPresent="no" xml:space="preserve">
 <w:ignoreElements w:val="http://schemas.microsoft.com/office/word/2003/wordml/sp2"/>
 <o:DocumentProperties>
  <o:Title>Export Consommations - Gestion MUN</o:Title>
  <o:Author>Application Gestion MUN</o:Author>
  <o:Created>''' + datetime.now().strftime("%Y-%m-%dT%H:%M:%S") + '''</o:Created>
 </o:DocumentProperties>
 <w:fonts>
  <w:defaultFonts w:ascii="Calibri" w:fareast="Calibri" w:h-ansi="Calibri" w:cs="Times New Roman"/>
 </w:fonts>
 <w:styles>
  <w:style w:type="paragraph" w:default="on" w:styleId="Normal">
   <w:name w:val="Normal"/>
   <w:pPr>
    <w:spacing w:after="0" w:line="240" w:line-rule="auto"/>
   </w:pPr>
   <w:rPr>
    <w:sz w:val="22"/>
    <w:sz-cs w:val="22"/>
    <w:lang w:val="FR" w:fareast="EN-US" w:bidi="AR-SA"/>
   </w:rPr>
  </w:style>
  <w:style w:type="paragraph" w:styleId="Heading1">
   <w:name w:val="heading 1"/>
   <w:pPr>
    <w:spacing w:before="240" w:after="60"/>
   </w:pPr>
   <w:rPr>
    <w:b/>
    <w:sz w:val="32"/>
    <w:sz-cs w:val="32"/>
   </w:rPr>
  </w:style>
 </w:styles>
 <w:body>
  <wx:sect>
   <w:p>
    <w:pPr>
     <w:pStyle w:val="Heading1"/>
     <w:jc w:val="center"/>
    </w:pPr>
    <w:r>
     <w:t>Export des Consommations - Gestion MUN</w:t>
    </w:r>
   </w:p>
   <w:p>
    <w:r>
     <w:rPr><w:b/></w:rPr>
     <w:t>Date d'export : </w:t>
    </w:r>
    <w:r>
     <w:t>''' + datetime.now().strftime("%d/%m/%Y à %H:%M") + '''</w:t>
    </w:r>
   </w:p>
   <w:p>
    <w:r>
     <w:rPr><w:b/></w:rPr>
     <w:t>Nombre d'enregistrements : </w:t>
    </w:r>
    <w:r>
     <w:t>''' + str(len(self.data)) + '''</w:t>
    </w:r>
   </w:p>
   <w:p/>
   <w:tbl>
    <w:tblPr>
     <w:tblW w:w="0" w:type="auto"/>
     <w:tblBorders>
      <w:top w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
      <w:left w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
      <w:bottom w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
      <w:right w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
      <w:insideH w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
      <w:insideV w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
     </w:tblBorders>
    </w:tblPr>
    <w:tblGrid>'''

        # Définir les largeurs de colonnes (en twips)
        col_widths = [1800, 1800, 2400, 1800, 1440, 1800, 1800, 2160]
        for width in col_widths:
            xml += f'\n     <w:gridCol w:w="{width}"/>'

        # En-têtes du tableau
        xml += '\n    </w:tblGrid>\n    <w:tr>'
        for field in fieldnames:
            xml += f'''
     <w:tc>
      <w:tcPr>
       <w:shd w:val="clear" w:color="auto" w:fill="4472C4"/>
      </w:tcPr>
      <w:p>
       <w:pPr>
        <w:jc w:val="center"/>
       </w:pPr>
       <w:r>
        <w:rPr>
         <w:b/>
         <w:color w:val="FFFFFF"/>
        </w:rPr>
        <w:t>{headers[field]}</w:t>
       </w:r>
      </w:p>
     </w:tc>'''

        xml += '\n    </w:tr>'

        # Données du tableau
        for i, item in enumerate(self.data):
            bg_color = "F2F2F2" if i % 2 == 0 else "FFFFFF"
            xml += '\n    <w:tr>'

            for field in fieldnames:
                value = str(item.get(field, ''))
                # Échapper les caractères XML
                value = value.replace('&', '&amp;').replace('<', '&lt;').replace('>', '&gt;')

                xml += f'''
     <w:tc>
      <w:tcPr>
       <w:shd w:val="clear" w:color="auto" w:fill="{bg_color}"/>
      </w:tcPr>
      <w:p>
       <w:r>
        <w:t>{value}</w:t>
       </w:r>
      </w:p>
     </w:tc>'''

            xml += '\n    </w:tr>'

        xml += '''
   </w:tbl>
   <w:p/>
   <w:p>
    <w:r>
     <w:rPr><w:i/></w:rPr>
     <w:t>Template généré par l'application Gestion MUN</w:t>
    </w:r>
   </w:p>
   <w:sectPr>
    <w:pgSz w:w="11906" w:h="16838"/>
    <w:pgMar w:top="1134" w:right="850" w:bottom="1134" w:left="1134" w:header="708" w:footer="708" w:gutter="0"/>
    <w:cols w:space="708"/>
   </w:sectPr>
  </wx:sect>
 </w:body>
</w:wordDocument>'''

        return xml

    def _create_simple_word_table(self):
        """Crée un tableau HTML simple optimisé pour Word"""
        fieldnames = ['nom_secteur', 'nom_unite', 'nom_piece', 'nom_categorie', 'date_periode',
                     'nombre_attribue', 'nombre_consomme', 'reste_non_consomme']

        headers = {
            'nom_secteur': 'Secteur',
            'nom_unite': 'Unité',
            'nom_piece': 'Pièce',
            'nom_categorie': 'Catégorie',
            'date_periode': 'Date',
            'nombre_attribue': 'Nombre Attribué',
            'nombre_consomme': 'Nombre Consommé',
            'reste_non_consomme': 'Reste Non Consommé'
        }

        # HTML simple et propre pour Word
        html = """<html>
<head>
<meta charset="UTF-8">
<title>Export Consommations - Gestion MUN</title>
</head>
<body>
<h1>Export des Consommations - Gestion MUN</h1>
<p><b>Date d'export :</b> """ + datetime.now().strftime("%d/%m/%Y à %H:%M") + """</p>
<p><b>Nombre d'enregistrements :</b> """ + str(len(self.data)) + """</p>

<table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse; width: 100%;">
<thead>
<tr style="background-color: #4472C4; color: white; font-weight: bold;">"""

        # En-têtes
        for field in fieldnames:
            html += f"\n<th>{headers[field]}</th>"

        html += "\n</tr>\n</thead>\n<tbody>"

        # Données avec alternance de couleurs
        for i, item in enumerate(self.data):
            bg_color = "#F2F2F2" if i % 2 == 0 else "#FFFFFF"
            html += f'\n<tr style="background-color: {bg_color};">'

            for field in fieldnames:
                value = str(item.get(field, ''))
                html += f"\n<td>{value}</td>"

            html += "\n</tr>"

        html += """
</tbody>
</table>

<p><i>Export généré par l'application Gestion MUN</i></p>
</body>
</html>"""

        return html

    def _create_rtf_table(self):
        """Crée un tableau RTF formaté avec les données de consommation"""
        fieldnames = ['nom_secteur', 'nom_unite', 'nom_piece', 'nom_categorie', 'date_periode',
                     'nombre_attribue', 'nombre_consomme', 'reste_non_consomme']

        headers = {
            'nom_secteur': 'Secteur',
            'nom_unite': 'Unité',
            'nom_piece': 'Pièce',
            'nom_categorie': 'Catégorie',
            'date_periode': 'Date',
            'nombre_attribue': 'Nombre Attribué',
            'nombre_consomme': 'Nombre Consommé',
            'reste_non_consomme': 'Reste Non Consommé'
        }

        # En-tête RTF
        rtf = r"""{\rtf1\ansi\deff0 {\fonttbl {\f0 Times New Roman;}}
{\colortbl;\red0\green0\blue0;\red255\green255\blue255;\red52\green152\blue219;}
\paperw11900\paperh16840\margl1440\margr1440\margt1440\margb1440
\f0\fs24

{\pard\qc\b\fs28\cf3 Export des Consommations - Gestion MUN\par}
\par
{\b Date d'export :} """ + datetime.now().strftime("%d/%m/%Y à %H:%M") + r"""\par
{\b Nombre d'enregistrements :} """ + str(len(self.data)) + r"""\par
\par

{\trowd\trgaph108\trleft-108"""

        # Définir les largeurs de colonnes (en twips, 1440 twips = 1 inch)
        col_widths = [1800, 1800, 2400, 1800, 1440, 1800, 1800, 2160]  # Total ≈ 15000 twips

        # Définir les positions des colonnes
        pos = 0
        for width in col_widths:
            pos += width
            rtf += f"\\cellx{pos}"

        rtf += "\n"

        # En-têtes du tableau
        rtf += r"\pard\intbl\b\cf3"
        for field in fieldnames:
            rtf += f"\\cell {headers[field]}"
        rtf += r"\row" + "\n"

        # Données du tableau
        for item in self.data:
            rtf += r"\trowd\trgaph108\trleft-108"

            # Redéfinir les cellules pour chaque ligne
            pos = 0
            for width in col_widths:
                pos += width
                rtf += f"\\cellx{pos}"

            rtf += "\n" + r"\pard\intbl"

            for field in fieldnames:
                value = str(item.get(field, ''))
                # Échapper les caractères spéciaux RTF
                value = value.replace('\\', '\\\\').replace('{', '\\{').replace('}', '\\}')
                rtf += f"\\cell {value}"

            rtf += r"\row" + "\n"

        # Fermeture du document RTF
        rtf += r"""
\par
\par
{\pard\qc\fs18 Export généré par l'application Gestion MUN\par}
}"""

        return rtf

    def export_custom(self):
        """Exporte les données avec sélection personnalisée des champs"""
        if not self.data:
            messagebox.showwarning("Attention", "Aucune donnée à exporter")
            return

        # Fenêtre de sélection des champs
        custom_window = tk.Toplevel(self)
        custom_window.title("Export Personnalisé")
        custom_window.geometry("500x600")
        custom_window.transient(self)
        custom_window.grab_set()

        # Centrer la fenêtre
        custom_window.update_idletasks()
        x = (custom_window.winfo_screenwidth() // 2) - (500 // 2)
        y = (custom_window.winfo_screenheight() // 2) - (600 // 2)
        custom_window.geometry(f"500x600+{x}+{y}")

        # Titre
        title_label = tk.Label(custom_window, text="Sélectionner les champs à exporter",
                              font=("Arial", 14, "bold"))
        title_label.pack(pady=10)

        # Frame pour les checkboxes
        fields_frame = tk.Frame(custom_window)
        fields_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Tous les champs disponibles avec leurs titres dans l'ordre d'affichage
        available_fields = {
            'nom_secteur': 'Secteur',
            'nom_unite': 'Unité',
            'nom_piece': 'Pièce',
            'nom_categorie': 'Catégorie',
            'date_periode': 'Date',
            'mois': 'Mois',
            'nombre_attribue': 'Nombre Attribué',
            'nombre_consomme': 'Nombre Consommé',
            'reste_non_consomme': 'Reste Non Consommé'
        }

        # Variables pour les checkboxes
        field_vars = {}

        # Créer les checkboxes
        for field, title in available_fields.items():
            var = tk.BooleanVar()
            # Sélectionner par défaut les champs principaux (incluant nom_unite)
            if field in ['nom_secteur', 'nom_unite', 'nom_piece', 'nom_categorie', 'date_periode',
                        'nombre_attribue', 'nombre_consomme', 'reste_non_consomme']:
                var.set(True)

            field_vars[field] = var

            checkbox = tk.Checkbutton(fields_frame, text=title, variable=var,
                                    font=("Arial", 11))
            checkbox.pack(anchor='w', pady=2)

        # Boutons de sélection rapide
        quick_frame = tk.Frame(custom_window)
        quick_frame.pack(fill='x', padx=20, pady=10)

        def select_all():
            for var in field_vars.values():
                var.set(True)

        def select_none():
            for var in field_vars.values():
                var.set(False)

        def select_default():
            for field, var in field_vars.items():
                if field in ['nom_secteur', 'nom_unite', 'nom_piece', 'nom_categorie', 'date_periode',
                           'nombre_attribue', 'nombre_consomme', 'reste_non_consomme']:
                    var.set(True)
                else:
                    var.set(False)

        tk.Button(quick_frame, text="Tout sélectionner", command=select_all).pack(side='left', padx=5)
        tk.Button(quick_frame, text="Tout désélectionner", command=select_none).pack(side='left', padx=5)
        tk.Button(quick_frame, text="Sélection par défaut", command=select_default).pack(side='left', padx=5)

        # Format d'export
        format_frame = tk.Frame(custom_window)
        format_frame.pack(fill='x', padx=20, pady=10)

        tk.Label(format_frame, text="Format d'export:", font=("Arial", 11, "bold")).pack(anchor='w')

        format_var = tk.StringVar(value="html")
        tk.Radiobutton(format_frame, text="🌐 HTML (universel)", variable=format_var, value="html").pack(anchor='w')
        tk.Radiobutton(format_frame, text="📈 Excel (compatible)", variable=format_var, value="excel").pack(anchor='w')
        tk.Radiobutton(format_frame, text="📄 Word (compatible)", variable=format_var, value="word").pack(anchor='w')

        # Boutons d'action
        button_frame = tk.Frame(custom_window)
        button_frame.pack(fill='x', padx=20, pady=20)

        def do_export():
            # Vérifier qu'au moins un champ est sélectionné
            selected_fields = [field for field, var in field_vars.items() if var.get()]

            if not selected_fields:
                messagebox.showwarning("Attention", "Veuillez sélectionner au moins un champ")
                return

            # Choisir le fichier selon le format
            format_type = format_var.get()

            if format_type == "html":
                filename = filedialog.asksaveasfilename(
                    defaultextension=".html",
                    filetypes=[("HTML files", "*.html"), ("All files", "*.*")],
                    title="Sauvegarder le fichier HTML"
                )
            elif format_type == "excel":
                filename = filedialog.asksaveasfilename(
                    defaultextension=".xls",
                    filetypes=[("Excel files", "*.xls"), ("All files", "*.*")],
                    title="Sauvegarder le fichier Excel"
                )
            else:  # word
                filename = filedialog.asksaveasfilename(
                    defaultextension=".doc",
                    filetypes=[("Word files", "*.doc"), ("All files", "*.*")],
                    title="Sauvegarder le fichier Word"
                )

            if filename:
                try:
                    if format_type == "html":
                        # Export HTML
                        html_content = self._create_custom_html_table(selected_fields, available_fields)
                        with open(filename, 'w', encoding='utf-8') as htmlfile:
                            htmlfile.write(html_content)

                    elif format_type == "excel":
                        # Export Excel (HTML compatible)
                        excel_html = self._create_custom_excel_html(selected_fields, available_fields)
                        with open(filename, 'w', encoding='utf-8') as file:
                            file.write(excel_html)

                    else:  # word
                        # Export Word (HTML compatible)
                        word_html = self._create_custom_word_html(selected_fields, available_fields)
                        with open(filename, 'w', encoding='utf-8') as file:
                            file.write(word_html)

                    custom_window.destroy()
                    messagebox.showinfo("Succès", f"Données exportées vers {filename}")
                except Exception as e:
                    messagebox.showerror("Erreur", f"Erreur lors de l'export: {str(e)}")

        tk.Button(button_frame, text="Exporter", command=do_export,
                 bg='green', fg='white', font=("Arial", 11, "bold")).pack(side='right', padx=5)
        tk.Button(button_frame, text="Annuler", command=custom_window.destroy).pack(side='right', padx=5)

    def _create_custom_html_table(self, selected_fields, available_fields):
        """Crée un tableau HTML personnalisé avec les champs sélectionnés"""
        html = """<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Export Personnalisé - Gestion MUN</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1 { color: #2c3e50; text-align: center; }
        table { border-collapse: collapse; width: 100%; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #3498db; color: white; font-weight: bold; }
        tr:nth-child(even) { background-color: #f2f2f2; }
        tr:hover { background-color: #e8f4fd; }
        .number { text-align: right; }
        .date { text-align: center; }
        .footer { margin-top: 20px; font-size: 12px; color: #666; text-align: center; }
    </style>
</head>
<body>
    <h1>📊 Export Personnalisé - Gestion MUN</h1>
    <p><strong>Date d'export :</strong> """ + datetime.now().strftime("%d/%m/%Y à %H:%M") + """</p>
    <p><strong>Champs sélectionnés :</strong> """ + str(len(selected_fields)) + """ / """ + str(len(available_fields)) + """</p>
    <p><strong>Nombre d'enregistrements :</strong> """ + str(len(self.data)) + """</p>

    <table>
        <thead>
            <tr>"""

        # En-têtes pour les champs sélectionnés
        for field in selected_fields:
            html += f"\n                <th>{available_fields[field]}</th>"

        html += """
            </tr>
        </thead>
        <tbody>"""

        # Données pour les champs sélectionnés
        for item in self.data:
            html += "\n            <tr>"
            for field in selected_fields:
                value = str(item.get(field, ''))
                css_class = ""

                if field in ['nombre_attribue', 'nombre_consomme', 'reste_non_consomme']:
                    css_class = ' class="number"'
                elif field == 'date_periode':
                    css_class = ' class="date"'

                html += f'\n                <td{css_class}>{value}</td>'
            html += "\n            </tr>"

        html += """
        </tbody>
    </table>

    <div class="footer">
        <p>📋 Export personnalisé généré par l'application Gestion MUN</p>
        <p>💡 Ce fichier peut être ouvert dans Excel, Word, ou tout navigateur web</p>
    </div>
</body>
</html>"""

        return html

    def _create_custom_excel_xml(self, selected_fields, available_fields):
        """Crée un fichier XML Excel personnalisé avec les champs sélectionnés"""
        # Utiliser la même structure que _create_excel_xml mais avec les champs sélectionnés
        xml = '''<?xml version="1.0" encoding="UTF-8"?>
<?mso-application progid="Excel.Sheet"?>
<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet"
 xmlns:o="urn:schemas-microsoft-com:office:office"
 xmlns:x="urn:schemas-microsoft-com:office:excel"
 xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet"
 xmlns:html="http://www.w3.org/TR/REC-html40">
 <DocumentProperties xmlns="urn:schemas-microsoft-com:office:office">
  <Title>Export Personnalisé - Gestion MUN</Title>
  <Author>Application Gestion MUN</Author>
  <Created>''' + datetime.now().strftime("%Y-%m-%dT%H:%M:%S") + '''</Created>
 </DocumentProperties>
 <ExcelWorkbook xmlns="urn:schemas-microsoft-com:office:excel">
  <WindowHeight>12000</WindowHeight>
  <WindowWidth>18000</WindowWidth>
  <WindowTopX>0</WindowTopX>
  <WindowTopY>0</WindowTopY>
  <ProtectStructure>False</ProtectStructure>
  <ProtectWindows>False</ProtectWindows>
 </ExcelWorkbook>
 <Styles>
  <Style ss:ID="Default" ss:Name="Normal">
   <Alignment ss:Vertical="Bottom"/>
   <Borders/>
   <Font ss:FontName="Calibri" x:Family="Swiss" ss:Size="11" ss:Color="#000000"/>
   <Interior/>
   <NumberFormat/>
   <Protection/>
  </Style>
  <Style ss:ID="s62">
   <Alignment ss:Horizontal="Center" ss:Vertical="Bottom"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="Calibri" x:Family="Swiss" ss:Size="11" ss:Color="#FFFFFF" ss:Bold="1"/>
   <Interior ss:Color="#4472C4" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="s63">
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="Calibri" x:Family="Swiss" ss:Size="11"/>
  </Style>
 </Styles>
 <Worksheet ss:Name="Consommations">
  <Table ss:ExpandedColumnCount="''' + str(len(selected_fields)) + '''" ss:ExpandedRowCount="''' + str(len(self.data) + 1) + '''" x:FullColumns="1" x:FullRows="1">'''

        # Définir les largeurs de colonnes dynamiquement
        col_width = 120
        for i in range(len(selected_fields)):
            xml += f'\n   <Column ss:Index="{i+1}" ss:AutoFitWidth="0" ss:Width="{col_width}"/>'

        # En-têtes
        xml += '\n   <Row ss:AutoFitHeight="0" ss:Height="20">'
        for field in selected_fields:
            xml += f'\n    <Cell ss:StyleID="s62"><Data ss:Type="String">{available_fields[field]}</Data></Cell>'
        xml += '\n   </Row>'

        # Données
        for item in self.data:
            xml += '\n   <Row>'
            for field in selected_fields:
                value = str(item.get(field, ''))
                # Échapper les caractères XML
                value = value.replace('&', '&amp;').replace('<', '&lt;').replace('>', '&gt;')

                # Déterminer le type de données
                if field in ['nombre_attribue', 'nombre_consomme', 'reste_non_consomme']:
                    data_type = "Number"
                else:
                    data_type = "String"

                xml += f'\n    <Cell ss:StyleID="s63"><Data ss:Type="{data_type}">{value}</Data></Cell>'
            xml += '\n   </Row>'

        xml += '''
  </Table>
  <WorksheetOptions xmlns="urn:schemas-microsoft-com:office:excel">
   <PageSetup>
    <Header x:Margin="0.3"/>
    <Footer x:Margin="0.3"/>
    <PageMargins x:Bottom="0.75" x:Left="0.7" x:Right="0.7" x:Top="0.75"/>
   </PageSetup>
   <Selected/>
   <Panes>
    <Pane>
     <Number>3</Number>
     <ActiveRow>1</ActiveRow>
     <ActiveCol>0</ActiveCol>
    </Pane>
   </Panes>
   <ProtectObjects>False</ProtectObjects>
   <ProtectScenarios>False</ProtectScenarios>
  </WorksheetOptions>
 </Worksheet>
</Workbook>'''

        return xml

    def _create_custom_rtf_table(self, selected_fields, available_fields):
        """Crée un tableau RTF personnalisé avec les champs sélectionnés"""
        # En-tête RTF
        rtf = r"""{\rtf1\ansi\deff0 {\fonttbl {\f0 Times New Roman;}}
{\colortbl;\red0\green0\blue0;\red255\green255\blue255;\red52\green152\blue219;}
\paperw11900\paperh16840\margl1440\margr1440\margt1440\margb1440
\f0\fs24

{\pard\qc\b\fs28\cf3 Export Personnalisé - Gestion MUN\par}
\par
{\b Date d'export :} """ + datetime.now().strftime("%d/%m/%Y à %H:%M") + r"""\par
{\b Champs sélectionnés :} """ + str(len(selected_fields)) + " / " + str(len(available_fields)) + r"""\par
{\b Nombre d'enregistrements :} """ + str(len(self.data)) + r"""\par
\par

{\trowd\trgaph108\trleft-108"""

        # Calculer les largeurs de colonnes dynamiquement
        total_width = 15000  # Largeur totale en twips
        col_width = total_width // len(selected_fields)

        # Définir les positions des colonnes
        pos = 0
        for _ in selected_fields:
            pos += col_width
            rtf += f"\\cellx{pos}"

        rtf += "\n"

        # En-têtes du tableau
        rtf += r"\pard\intbl\b\cf3"
        for field in selected_fields:
            rtf += f"\\cell {available_fields[field]}"
        rtf += r"\row" + "\n"

        # Données du tableau
        for item in self.data:
            rtf += r"\trowd\trgaph108\trleft-108"

            # Redéfinir les cellules pour chaque ligne
            pos = 0
            for _ in selected_fields:
                pos += col_width
                rtf += f"\\cellx{pos}"

            rtf += "\n" + r"\pard\intbl"

            for field in selected_fields:
                value = str(item.get(field, ''))
                # Échapper les caractères spéciaux RTF
                value = value.replace('\\', '\\\\').replace('{', '\\{').replace('}', '\\}')
                rtf += f"\\cell {value}"

            rtf += r"\row" + "\n"

        # Fermeture du document RTF
        rtf += r"""
\par
\par
{\pard\qc\fs18 Export personnalisé généré par l'application Gestion MUN\par}
}"""

        return rtf

    def _create_custom_word_table(self, selected_fields, available_fields):
        """Crée un tableau Word personnalisé avec les champs sélectionnés"""
        # HTML simple et propre pour Word
        html = """<html>
<head>
<meta charset="UTF-8">
<title>Export Personnalisé - Gestion MUN</title>
</head>
<body>
<h1>Export Personnalisé - Gestion MUN</h1>
<p><b>Date d'export :</b> """ + datetime.now().strftime("%d/%m/%Y à %H:%M") + """</p>
<p><b>Champs sélectionnés :</b> """ + str(len(selected_fields)) + """ / """ + str(len(available_fields)) + """</p>
<p><b>Nombre d'enregistrements :</b> """ + str(len(self.data)) + """</p>

<table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse; width: 100%;">
<thead>
<tr style="background-color: #4472C4; color: white; font-weight: bold;">"""

        # En-têtes pour les champs sélectionnés
        for field in selected_fields:
            html += f"\n<th>{available_fields[field]}</th>"

        html += "\n</tr>\n</thead>\n<tbody>"

        # Données avec alternance de couleurs
        for i, item in enumerate(self.data):
            bg_color = "#F2F2F2" if i % 2 == 0 else "#FFFFFF"
            html += f'\n<tr style="background-color: {bg_color};">'

            for field in selected_fields:
                value = str(item.get(field, ''))
                html += f"\n<td>{value}</td>"

            html += "\n</tr>"

        html += """
</tbody>
</table>

<p><i>Export personnalisé généré par l'application Gestion MUN</i></p>
</body>
</html>"""

        return html

    def _create_custom_excel_html(self, selected_fields, available_fields):
        """Crée un fichier HTML Excel personnalisé avec les champs sélectionnés"""
        # HTML optimisé pour Excel
        html = """<html xmlns:o="urn:schemas-microsoft-com:office:office"
xmlns:x="urn:schemas-microsoft-com:office:excel"
xmlns="http://www.w3.org/TR/REC-html40">
<head>
<meta charset="UTF-8">
<meta name="ProgId" content="Excel.Sheet">
<meta name="Generator" content="Gestion MUN">
<style>
.header { background-color: #4472C4; color: white; font-weight: bold; text-align: center; }
.data { border: 1px solid #000000; }
.number { text-align: right; }
table { border-collapse: collapse; }
</style>
</head>
<body>
<table border="1">
<tr>"""

        # En-têtes pour les champs sélectionnés
        for field in selected_fields:
            html += f'\n<th class="header">{available_fields[field]}</th>'

        html += '\n</tr>'

        # Données pour les champs sélectionnés
        for item in self.data:
            html += '\n<tr>'
            for field in selected_fields:
                value = str(item.get(field, ''))
                css_class = "data"
                if field in ['nombre_attribue', 'nombre_consomme', 'reste_non_consomme']:
                    css_class += " number"

                html += f'\n<td class="{css_class}">{value}</td>'
            html += '\n</tr>'

        html += """
</table>
</body>
</html>"""

        return html

    def _create_custom_word_html(self, selected_fields, available_fields):
        """Crée un fichier HTML Word personnalisé avec les champs sélectionnés"""
        # HTML optimisé pour Word
        html = """<html xmlns:o="urn:schemas-microsoft-com:office:office"
xmlns:w="urn:schemas-microsoft-com:office:word"
xmlns="http://www.w3.org/TR/REC-html40">
<head>
<meta charset="UTF-8">
<meta name="ProgId" content="Word.Document">
<meta name="Generator" content="Gestion MUN">
<style>
body { font-family: Calibri, sans-serif; margin: 20px; }
h1 { color: #2c3e50; text-align: center; font-size: 18pt; }
.info { margin: 10px 0; font-weight: bold; }
table { border-collapse: collapse; width: 100%; margin: 20px 0; }
.header { background-color: #4472C4; color: white; font-weight: bold; text-align: center; padding: 8px; border: 1px solid #000; }
.data { padding: 6px; border: 1px solid #000; }
.alt { background-color: #F2F2F2; }
.number { text-align: right; }
</style>
</head>
<body>
<h1>Export Personnalisé - Gestion MUN</h1>
<p class="info">Date d'export : """ + datetime.now().strftime("%d/%m/%Y à %H:%M") + """</p>
<p class="info">Champs sélectionnés : """ + str(len(selected_fields)) + """ / """ + str(len(available_fields)) + """</p>
<p class="info">Nombre d'enregistrements : """ + str(len(self.data)) + """</p>

<table>
<tr>"""

        # En-têtes pour les champs sélectionnés
        for field in selected_fields:
            html += f'\n<th class="header">{available_fields[field]}</th>'

        html += '\n</tr>'

        # Données avec alternance de couleurs
        for i, item in enumerate(self.data):
            alt_class = " alt" if i % 2 == 0 else ""
            html += '\n<tr>'
            for field in selected_fields:
                value = str(item.get(field, ''))
                css_class = f"data{alt_class}"
                if field in ['nombre_attribue', 'nombre_consomme', 'reste_non_consomme']:
                    css_class += " number"

                html += f'\n<td class="{css_class}">{value}</td>'
            html += '\n</tr>'

        html += """
</table>

<p><i>Export personnalisé généré par l'application Gestion MUN</i></p>
</body>
</html>"""

        return html

class StatistiquesFrame(tk.Frame):
    """Frame pour les statistiques avec différents graphiques"""

    def __init__(self, parent, consommation_model, unite_model, secteur_model, piece_model, periode_model):
        super().__init__(parent)
        self.consommation_model = consommation_model
        self.unite_model = unite_model
        self.secteur_model = secteur_model
        self.piece_model = piece_model
        self.periode_model = periode_model

        self.setup_ui()

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        # Frame principal avec sidebar et zone graphique
        main_frame = tk.Frame(self)
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)

        # Sidebar pour les options
        sidebar = tk.Frame(main_frame, width=250, bg='lightgray')
        sidebar.pack(side='left', fill='y', padx=(0, 10))
        sidebar.pack_propagate(False)

        # Zone graphique
        self.graph_frame = tk.Frame(main_frame, bg='white')
        self.graph_frame.pack(side='right', fill='both', expand=True)

        # Titre de la sidebar
        title_label = tk.Label(sidebar, text="Options Statistiques",
                              font=("Arial", 14, "bold"), bg='lightgray')
        title_label.pack(pady=10)

        # Boutons pour différents types de graphiques
        self.create_sidebar_buttons(sidebar)

        # Message initial
        initial_label = tk.Label(self.graph_frame,
                                text="Sélectionnez un type de statistique\ndans la barre latérale",
                                font=("Arial", 12), fg='gray')
        initial_label.pack(expand=True)

    def create_sidebar_buttons(self, parent):
        """Crée les boutons de la sidebar"""
        buttons_config = [
            ("📊 Consommation par Unité", self.show_consommation_par_unite),
            ("🏢 Consommation par Secteur", self.show_consommation_par_secteur),
            ("📅 Consommation par Période", self.show_consommation_par_periode),
            ("🔧 Consommation par Pièce", self.show_consommation_par_piece),
            ("📈 Évolution Temporelle", self.show_evolution_temporelle),
            ("🎯 Efficacité par Unité", self.show_efficacite_par_unite),
            ("📋 Top 10 Consommateurs", self.show_top_consommateurs),
            ("⚖️ Comparaison Secteurs", self.show_comparaison_secteurs),
            ("📊 Répartition par Catégorie", self.show_repartition_categories),
            ("📉 Analyse des Restes", self.show_analyse_restes)
        ]

        for text, command in buttons_config:
            btn = tk.Button(parent, text=text, command=command,
                           width=25, pady=5, anchor='w',
                           bg='white', relief='raised')
            btn.pack(pady=2, padx=10, fill='x')

    def clear_graph_frame(self):
        """Efface le contenu de la zone graphique"""
        for widget in self.graph_frame.winfo_children():
            widget.destroy()

    def show_consommation_par_unite(self):
        """Affiche un graphique de consommation par unité"""
        self.clear_graph_frame()

        try:
            import matplotlib.pyplot as plt
            from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
            import matplotlib
            matplotlib.use('TkAgg')

            # Récupérer les données
            consommations = self.consommation_model.get_all()

            if not consommations:
                self.show_no_data_message()
                return

            # Calculer les totaux par unité
            unite_totals = {}
            for cons in consommations:
                unite = cons.get('nom_unite', 'Inconnu')
                consomme = cons.get('nombre_consomme', 0)
                if unite in unite_totals:
                    unite_totals[unite] += consomme
                else:
                    unite_totals[unite] = consomme

            # Créer le graphique
            fig, ax = plt.subplots(figsize=(10, 6))
            unites = list(unite_totals.keys())[:10]  # Top 10
            values = [unite_totals[u] for u in unites]

            bars = ax.bar(unites, values, color='skyblue', edgecolor='navy')
            ax.set_title('Top 10 - Consommation par Unité', fontsize=14, fontweight='bold')
            ax.set_xlabel('Unités')
            ax.set_ylabel('Quantité Consommée')
            ax.tick_params(axis='x', rotation=45)

            # Ajouter les valeurs sur les barres
            for bar, value in zip(bars, values):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                       f'{value}', ha='center', va='bottom')

            plt.tight_layout()

            # Intégrer dans Tkinter
            canvas = FigureCanvasTkAgg(fig, self.graph_frame)
            canvas.draw()
            canvas.get_tk_widget().pack(fill='both', expand=True)

        except Exception as e:
            self.show_error_message(f"Erreur lors de la création du graphique: {str(e)}")

    def show_consommation_par_secteur(self):
        """Affiche un graphique de consommation par secteur"""
        self.clear_graph_frame()

        try:
            import matplotlib.pyplot as plt
            from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
            import matplotlib
            matplotlib.use('TkAgg')

            consommations = self.consommation_model.get_all()

            if not consommations:
                self.show_no_data_message()
                return

            # Calculer les totaux par secteur
            secteur_totals = {}
            for cons in consommations:
                secteur = cons.get('nom_secteur', 'Inconnu')
                consomme = cons.get('nombre_consomme', 0)
                if secteur in secteur_totals:
                    secteur_totals[secteur] += consomme
                else:
                    secteur_totals[secteur] = consomme

            # Créer un graphique en camembert
            fig, ax = plt.subplots(figsize=(8, 8))
            secteurs = list(secteur_totals.keys())
            values = list(secteur_totals.values())
            colors = ['#ff9999', '#66b3ff', '#99ff99', '#ffcc99', '#ff99cc']

            wedges, texts, autotexts = ax.pie(values, labels=secteurs, autopct='%1.1f%%',
                                             colors=colors[:len(secteurs)], startangle=90)
            ax.set_title('Répartition de la Consommation par Secteur',
                        fontsize=14, fontweight='bold')

            plt.tight_layout()

            canvas = FigureCanvasTkAgg(fig, self.graph_frame)
            canvas.draw()
            canvas.get_tk_widget().pack(fill='both', expand=True)

        except Exception as e:
            self.show_error_message(f"Erreur lors de la création du graphique: {str(e)}")

    def show_consommation_par_periode(self):
        """Affiche un graphique de consommation par période"""
        self.clear_graph_frame()

        try:
            import matplotlib.pyplot as plt
            from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
            import matplotlib
            matplotlib.use('TkAgg')

            consommations = self.consommation_model.get_all()

            if not consommations:
                self.show_no_data_message()
                return

            # Calculer les totaux par période
            periode_totals = {}
            for cons in consommations:
                date_periode = str(cons.get('date_periode', 'Inconnu'))
                consomme = cons.get('nombre_consomme', 0)
                if date_periode in periode_totals:
                    periode_totals[date_periode] += consomme
                else:
                    periode_totals[date_periode] = consomme

            # Créer le graphique linéaire
            fig, ax = plt.subplots(figsize=(10, 6))
            periodes = sorted(periode_totals.keys())
            values = [periode_totals[p] for p in periodes]

            ax.plot(periodes, values, marker='o', linewidth=2, markersize=8, color='green')
            ax.set_title('Évolution de la Consommation par Période',
                        fontsize=14, fontweight='bold')
            ax.set_xlabel('Périodes')
            ax.set_ylabel('Quantité Consommée')
            ax.tick_params(axis='x', rotation=45)
            ax.grid(True, alpha=0.3)

            plt.tight_layout()

            canvas = FigureCanvasTkAgg(fig, self.graph_frame)
            canvas.draw()
            canvas.get_tk_widget().pack(fill='both', expand=True)

        except Exception as e:
            self.show_error_message(f"Erreur lors de la création du graphique: {str(e)}")

    def show_no_data_message(self):
        """Affiche un message quand il n'y a pas de données"""
        label = tk.Label(self.graph_frame,
                        text="Aucune donnée de consommation disponible\n\n"
                             "Ajoutez des consommations dans l'onglet\n"
                             "'Consommations' pour voir les statistiques",
                        font=("Arial", 12), fg='orange')
        label.pack(expand=True)

    def show_error_message(self, message):
        """Affiche un message d'erreur"""
        label = tk.Label(self.graph_frame, text=f"Erreur:\n{message}",
                        font=("Arial", 10), fg='red')
        label.pack(expand=True)

    def show_consommation_par_piece(self):
        """Affiche un graphique de consommation par pièce"""
        self.clear_graph_frame()

        try:
            import matplotlib.pyplot as plt
            from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
            import matplotlib
            matplotlib.use('TkAgg')

            consommations = self.consommation_model.get_all()

            if not consommations:
                self.show_no_data_message()
                return

            # Calculer les totaux par pièce
            piece_totals = {}
            for cons in consommations:
                piece = cons.get('nom_piece', 'Inconnu')
                consomme = cons.get('nombre_consomme', 0)
                if piece in piece_totals:
                    piece_totals[piece] += consomme
                else:
                    piece_totals[piece] = consomme

            # Trier et prendre le top 15
            sorted_pieces = sorted(piece_totals.items(), key=lambda x: x[1], reverse=True)[:15]
            pieces = [item[0] for item in sorted_pieces]
            values = [item[1] for item in sorted_pieces]

            # Créer un graphique horizontal
            fig, ax = plt.subplots(figsize=(10, 8))
            bars = ax.barh(pieces, values, color='lightcoral', edgecolor='darkred')
            ax.set_title('Top 15 - Consommation par Pièce', fontsize=14, fontweight='bold')
            ax.set_xlabel('Quantité Consommée')
            ax.set_ylabel('Pièces')

            # Ajouter les valeurs
            for bar, value in zip(bars, values):
                width = bar.get_width()
                ax.text(width + 0.5, bar.get_y() + bar.get_height()/2.,
                       f'{value}', ha='left', va='center')

            plt.tight_layout()

            canvas = FigureCanvasTkAgg(fig, self.graph_frame)
            canvas.draw()
            canvas.get_tk_widget().pack(fill='both', expand=True)

        except Exception as e:
            self.show_error_message(f"Erreur lors de la création du graphique: {str(e)}")

    def show_evolution_temporelle(self):
        """Affiche l'évolution temporelle des consommations"""
        self.clear_graph_frame()

        try:
            import matplotlib.pyplot as plt
            from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
            import matplotlib
            matplotlib.use('TkAgg')

            consommations = self.consommation_model.get_all()

            if not consommations:
                self.show_no_data_message()
                return

            # Grouper par période et calculer attribué vs consommé
            periode_data = {}
            for cons in consommations:
                periode = str(cons.get('date_periode', 'Inconnu'))
                attribue = cons.get('nombre_attribue', 0)
                consomme = cons.get('nombre_consomme', 0)

                if periode not in periode_data:
                    periode_data[periode] = {'attribue': 0, 'consomme': 0}

                periode_data[periode]['attribue'] += attribue
                periode_data[periode]['consomme'] += consomme

            # Créer le graphique
            fig, ax = plt.subplots(figsize=(12, 6))
            periodes = sorted(periode_data.keys())
            attribues = [periode_data[p]['attribue'] for p in periodes]
            consommes = [periode_data[p]['consomme'] for p in periodes]

            ax.plot(periodes, attribues, marker='s', label='Attribué', linewidth=2, color='blue')
            ax.plot(periodes, consommes, marker='o', label='Consommé', linewidth=2, color='red')

            ax.set_title('Évolution Temporelle: Attribué vs Consommé', fontsize=14, fontweight='bold')
            ax.set_xlabel('Périodes')
            ax.set_ylabel('Quantité')
            ax.legend()
            ax.grid(True, alpha=0.3)
            ax.tick_params(axis='x', rotation=45)

            plt.tight_layout()

            canvas = FigureCanvasTkAgg(fig, self.graph_frame)
            canvas.draw()
            canvas.get_tk_widget().pack(fill='both', expand=True)

        except Exception as e:
            self.show_error_message(f"Erreur lors de la création du graphique: {str(e)}")

    def show_efficacite_par_unite(self):
        """Affiche l'efficacité (% consommé/attribué) par unité"""
        self.clear_graph_frame()

        try:
            import matplotlib.pyplot as plt
            from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
            import matplotlib
            matplotlib.use('TkAgg')

            consommations = self.consommation_model.get_all()

            if not consommations:
                self.show_no_data_message()
                return

            # Calculer l'efficacité par unité
            unite_data = {}
            for cons in consommations:
                unite = cons.get('nom_unite', 'Inconnu')
                attribue = cons.get('nombre_attribue', 0)
                consomme = cons.get('nombre_consomme', 0)

                if unite not in unite_data:
                    unite_data[unite] = {'attribue': 0, 'consomme': 0}

                unite_data[unite]['attribue'] += attribue
                unite_data[unite]['consomme'] += consomme

            # Calculer les pourcentages d'efficacité
            efficacites = {}
            for unite, data in unite_data.items():
                if data['attribue'] > 0:
                    efficacite = (data['consomme'] / data['attribue']) * 100
                    efficacites[unite] = efficacite

            # Trier et prendre le top 10
            sorted_efficacites = sorted(efficacites.items(), key=lambda x: x[1], reverse=True)[:10]
            unites = [item[0] for item in sorted_efficacites]
            values = [item[1] for item in sorted_efficacites]

            # Créer le graphique
            fig, ax = plt.subplots(figsize=(10, 6))
            colors = ['green' if v <= 100 else 'orange' if v <= 120 else 'red' for v in values]
            bars = ax.bar(unites, values, color=colors, alpha=0.7, edgecolor='black')

            ax.set_title('Top 10 - Efficacité par Unité (% Consommé/Attribué)',
                        fontsize=14, fontweight='bold')
            ax.set_xlabel('Unités')
            ax.set_ylabel('Efficacité (%)')
            ax.tick_params(axis='x', rotation=45)
            ax.axhline(y=100, color='red', linestyle='--', alpha=0.7, label='100% (Optimal)')
            ax.legend()

            # Ajouter les valeurs
            for bar, value in zip(bars, values):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + 1,
                       f'{value:.1f}%', ha='center', va='bottom')

            plt.tight_layout()

            canvas = FigureCanvasTkAgg(fig, self.graph_frame)
            canvas.draw()
            canvas.get_tk_widget().pack(fill='both', expand=True)

        except Exception as e:
            self.show_error_message(f"Erreur lors de la création du graphique: {str(e)}")

    def show_top_consommateurs(self):
        """Affiche le top 10 des plus gros consommateurs"""
        self.show_consommation_par_unite()  # Réutilise la même logique

    def show_comparaison_secteurs(self):
        """Affiche une comparaison détaillée entre secteurs"""
        self.show_consommation_par_secteur()  # Réutilise la même logique

    def show_repartition_categories(self):
        """Affiche la répartition par catégorie de pièces"""
        self.clear_graph_frame()

        try:
            import matplotlib.pyplot as plt
            from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
            import matplotlib
            matplotlib.use('TkAgg')

            consommations = self.consommation_model.get_all()

            if not consommations:
                self.show_no_data_message()
                return

            # Calculer les totaux par catégorie
            categorie_totals = {}
            for cons in consommations:
                categorie = cons.get('nom_categorie', 'Inconnu')
                consomme = cons.get('nombre_consomme', 0)
                if categorie in categorie_totals:
                    categorie_totals[categorie] += consomme
                else:
                    categorie_totals[categorie] = consomme

            # Créer un graphique en barres
            fig, ax = plt.subplots(figsize=(8, 6))
            categories = list(categorie_totals.keys())
            values = list(categorie_totals.values())
            colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57']

            bars = ax.bar(categories, values, color=colors[:len(categories)],
                         edgecolor='black', alpha=0.8)
            ax.set_title('Consommation par Catégorie de Pièces',
                        fontsize=14, fontweight='bold')
            ax.set_xlabel('Catégories')
            ax.set_ylabel('Quantité Consommée')

            # Ajouter les valeurs
            for bar, value in zip(bars, values):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                       f'{value}', ha='center', va='bottom', fontweight='bold')

            plt.tight_layout()

            canvas = FigureCanvasTkAgg(fig, self.graph_frame)
            canvas.draw()
            canvas.get_tk_widget().pack(fill='both', expand=True)

        except Exception as e:
            self.show_error_message(f"Erreur lors de la création du graphique: {str(e)}")

    def show_analyse_restes(self):
        """Affiche l'analyse des restes non consommés"""
        self.clear_graph_frame()

        try:
            import matplotlib.pyplot as plt
            from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
            import matplotlib
            matplotlib.use('TkAgg')

            consommations = self.consommation_model.get_all()

            if not consommations:
                self.show_no_data_message()
                return

            # Analyser les restes par unité
            unite_restes = {}
            for cons in consommations:
                unite = cons.get('nom_unite', 'Inconnu')
                reste = cons.get('reste_non_consomme', 0)
                if unite in unite_restes:
                    unite_restes[unite] += reste
                else:
                    unite_restes[unite] = reste

            # Filtrer les unités avec des restes significatifs
            unite_restes_filtered = {k: v for k, v in unite_restes.items() if v > 0}

            if not unite_restes_filtered:
                label = tk.Label(self.graph_frame,
                               text="Aucun reste non consommé détecté\n\nToutes les unités ont une consommation optimale!",
                               font=("Arial", 12), fg='green')
                label.pack(expand=True)
                return

            # Trier et prendre le top 10
            sorted_restes = sorted(unite_restes_filtered.items(), key=lambda x: x[1], reverse=True)[:10]
            unites = [item[0] for item in sorted_restes]
            values = [item[1] for item in sorted_restes]

            # Créer le graphique
            fig, ax = plt.subplots(figsize=(10, 6))
            bars = ax.bar(unites, values, color='orange', alpha=0.7, edgecolor='darkorange')
            ax.set_title('Top 10 - Analyse des Restes Non Consommés par Unité',
                        fontsize=14, fontweight='bold')
            ax.set_xlabel('Unités')
            ax.set_ylabel('Quantité Non Consommée')
            ax.tick_params(axis='x', rotation=45)

            # Ajouter les valeurs
            for bar, value in zip(bars, values):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                       f'{value}', ha='center', va='bottom')

            plt.tight_layout()

            canvas = FigureCanvasTkAgg(fig, self.graph_frame)
            canvas.draw()
            canvas.get_tk_widget().pack(fill='both', expand=True)

        except Exception as e:
            self.show_error_message(f"Erreur lors de la création du graphique: {str(e)}")

class MainApplication:
    """Application principale"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Gestion MUN")
        self.root.geometry("1000x700")
        
        # Barre de statut
        self.status_bar = ttk.Label(self.root, text="Prêt", relief='sunken')
        self.status_bar.pack(side='bottom', fill='x')

        # Créer le notebook (onglets)
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill='both', expand=True, padx=10, pady=10)

        # Créer les onglets
        self.create_tabs()
    
    def create_tabs(self):
        """Crée les onglets pour chaque table"""
        try:
            # Onglet Secteurs (personnalisé)
            secteur_model = SecteurModel()
            secteur_frame = SecteurFrame(self.notebook, "Secteurs", secteur_model)
            self.notebook.add(secteur_frame, text="Secteurs")

            # Onglet Unités (personnalisé)
            unite_frame = UniteFrame(self.notebook, "Unités", UniteModel(), secteur_model)
            self.notebook.add(unite_frame, text="Unités")

            # Modèle catégorie pour les pièces (sans onglet visible)
            categorie_model = CategoriePieceModel()

            # Onglet Pièces (personnalisé)
            piece_frame = PieceFrame(self.notebook, "Pièces", PieceModel(), categorie_model)
            self.notebook.add(piece_frame, text="Pièces")

            # Onglet Périodes (personnalisé)
            periode_model = PeriodeModel()
            periode_frame = PeriodeFrame(self.notebook, "Périodes", periode_model)
            self.notebook.add(periode_frame, text="Périodes")

            # Onglet Consommations (personnalisé avec formulaires avancés)
            consommation_model = ConsommationModel()
            unite_model_stats = UniteModel()
            piece_model_stats = PieceModel()

            consommation_frame = ConsommationFrame(
                self.notebook,
                "Consommations",
                consommation_model,
                unite_model_stats,
                piece_model_stats,
                periode_model
            )
            self.notebook.add(consommation_frame, text="Consommations")

            # Onglet Statistiques (nouveau avec graphiques)
            statistiques_frame = StatistiquesFrame(
                self.notebook,
                consommation_model,
                unite_model_stats,
                secteur_model,
                piece_model_stats,
                periode_model
            )
            self.notebook.add(statistiques_frame, text="Statistiques")
            
            self.status_bar.config(text="Application chargée avec succès")
            
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de la création des onglets: {str(e)}")
            self.status_bar.config(text="Erreur lors du chargement")
    
    def run(self):
        """Lance l'application"""
        self.root.mainloop()

def main():
    """Fonction principale"""
    try:
        app = MainApplication()
        app.run()
    except Exception as e:
        print(f"Erreur lors du lancement de l'application: {e}")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Test final des fonctionnalités de l'onglet Consommations
"""

import sys
import os

# Ajouter le répertoire src au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_consommation_display():
    """Teste l'affichage des consommations avec périodes enregistrées"""
    print("=== Test de l'affichage des consommations ===")
    
    try:
        from models.consommation import ConsommationModel
        from models.periode import PeriodeModel
        
        consommation_model = ConsommationModel()
        periode_model = PeriodeModel()
        
        # Vérifier les périodes disponibles
        periodes = periode_model.get_all()
        print(f"Périodes enregistrées: {len(periodes)}")
        
        if periodes:
            print("Exemples de périodes:")
            for i, per in enumerate(periodes[:5]):
                print(f"  {i+1}. ID: {per.get('id_periode')}, Date: {per.get('date_periode')}, Mois: {per.get('mois')}")
        
        # Vérifier les consommations
        consommations = consommation_model.get_all()
        print(f"\nConsommations enregistrées: {len(consommations)}")
        
        if consommations:
            print("\nStructure des données de consommation:")
            first_cons = consommations[0]
            for key, value in first_cons.items():
                print(f"  {key}: {value}")
            
            print("\nExemples de consommations:")
            for i, cons in enumerate(consommations[:5]):
                unite = cons.get('nom_unite', 'N/A')
                piece = cons.get('nom_piece', 'N/A')
                date_periode = cons.get('date_periode', 'N/A')
                mois = cons.get('mois', 'N/A')
                attribue = cons.get('nombre_attribue', 0)
                consomme = cons.get('nombre_consomme', 0)
                reste = cons.get('reste_non_consomme', 0)
                
                print(f"  {i+1}. {unite} - {piece}")
                print(f"      Période: {date_periode} (Mois {mois})")
                print(f"      Attribué: {attribue}, Consommé: {consomme}, Reste: {reste}")
        
        # Vérifier que les périodes dans les consommations correspondent aux périodes enregistrées
        periodes_ids_enregistrees = {p.get('id_periode') for p in periodes}
        periodes_ids_consommations = {c.get('id_periode') for c in consommations}
        
        periodes_orphelines = periodes_ids_consommations - periodes_ids_enregistrees
        if periodes_orphelines:
            print(f"\n⚠️ Périodes orphelines dans les consommations: {periodes_orphelines}")
        else:
            print("\n✅ Toutes les périodes des consommations sont enregistrées")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_periode_selection_format():
    """Teste le format de sélection des périodes pour les formulaires"""
    print("\n=== Test du format de sélection des périodes ===")
    
    try:
        from models.periode import PeriodeModel
        
        periode_model = PeriodeModel()
        periodes = periode_model.get_all()
        
        print("Format d'affichage pour les listes déroulantes:")
        periode_options = []
        periode_map = {}
        
        for per in periodes:
            display_text = f"{per.get('date_periode')} (Mois {per.get('mois')})"
            periode_options.append(display_text)
            periode_map[display_text] = per.get('id_periode')
            print(f"  '{display_text}' → ID: {per.get('id_periode')}")
        
        print(f"\nTotal d'options disponibles: {len(periode_options)}")
        print(f"Mapping créé: {len(periode_map)} entrées")
        
        if len(periode_options) == len(periode_map):
            print("✅ Format de sélection correct")
            return True
        else:
            print("❌ Problème dans le format de sélection")
            return False
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_crud_operations():
    """Teste les opérations CRUD sur les consommations"""
    print("\n=== Test des opérations CRUD ===")
    
    try:
        from models.consommation import ConsommationModel
        from models.unite import UniteModel
        from models.piece import PieceModel
        from models.periode import PeriodeModel
        
        # Modèles
        consommation_model = ConsommationModel()
        unite_model = UniteModel()
        piece_model = PieceModel()
        periode_model = PeriodeModel()
        
        # Récupérer des données pour le test
        unites = unite_model.get_all()
        pieces = piece_model.get_all()
        periodes = periode_model.get_all()
        
        if not unites or not pieces or not periodes:
            print("❌ Données insuffisantes pour le test CRUD")
            return False
        
        # Test de création
        unite_id = unites[0]['id_unite']
        piece_id = pieces[0]['id_piece']
        periode_id = periodes[0]['id_periode']
        
        print(f"Test avec Unité ID: {unite_id}, Pièce ID: {piece_id}, Période ID: {periode_id}")
        
        # Créer une consommation de test
        consommation_id = consommation_model.create(unite_id, piece_id, periode_id, 1000, 750)
        
        if consommation_id:
            print(f"✅ Création réussie - ID: {consommation_id}")
            
            # Test de modification
            success_update = consommation_model.update(consommation_id, unite_id, piece_id, periode_id, 1200, 900)
            if success_update:
                print("✅ Modification réussie")
            else:
                print("❌ Échec de la modification")
            
            # Vérifier les données modifiées
            consommations = consommation_model.get_all()
            test_consommation = None
            for cons in consommations:
                if cons.get('id_consommation') == consommation_id:
                    test_consommation = cons
                    break
            
            if test_consommation:
                print(f"Données après modification:")
                print(f"  Attribué: {test_consommation.get('nombre_attribue')}")
                print(f"  Consommé: {test_consommation.get('nombre_consomme')}")
                print(f"  Reste: {test_consommation.get('reste_non_consomme')}")
            
            # Test de suppression
            success_delete = consommation_model.delete(consommation_id)
            if success_delete:
                print("✅ Suppression réussie")
                return True
            else:
                print("❌ Échec de la suppression")
                return False
        else:
            print("❌ Échec de la création")
            return False
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Fonction principale"""
    print("=== Test final des fonctionnalités Consommations ===\n")
    
    results = []
    results.append(test_consommation_display())
    results.append(test_periode_selection_format())
    results.append(test_crud_operations())
    
    success_count = sum(results)
    total_count = len(results)
    
    print(f"\n=== Résultats ===")
    print(f"Tests réussis: {success_count}/{total_count}")
    
    if success_count == total_count:
        print("\n✅ Toutes les fonctionnalités sont opérationnelles!")
        print("\nL'onglet Consommations dispose maintenant de:")
        print("- ✅ Affichage des périodes enregistrées uniquement")
        print("- ✅ Sélection de périodes dans les formulaires")
        print("- ✅ Boutons Ajouter/Modifier/Supprimer opérationnels")
        print("- ✅ Calcul automatique des restes")
        print("- ✅ Validation complète des données")
        print("- ✅ Interface utilisateur optimisée")
        
        print("\nVous pouvez maintenant tester dans l'application:")
        print("1. Onglet 'Consommations' → Voir les données avec périodes")
        print("2. Bouton 'Ajouter' → Sélectionner une période existante")
        print("3. Bouton 'Modifier' → Modifier une consommation existante")
        print("4. Bouton 'Supprimer' → Supprimer une consommation")
    else:
        print(f"\n⚠️ {total_count - success_count} test(s) ont échoué")

if __name__ == "__main__":
    main()

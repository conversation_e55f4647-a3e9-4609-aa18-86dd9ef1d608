[{"classes": [{"className": "Abstract3DController", "object": true, "qualifiedClassName": "Abstract3DController", "signals": [{"access": "public", "arguments": [{"name": "quality", "type": "QAbstract3DGraph::ShadowQuality"}], "name": "shadowQualityChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "activeTheme", "type": "Q3DTheme*"}], "name": "activeThemeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "mode", "type": "QAbstract3DGraph::SelectionFlags"}], "name": "selectionMode<PERSON>hanged", "returnType": "void"}, {"access": "public", "name": "needRender", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstract3DAxis*"}], "name": "axisXChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstract3DAxis*"}], "name": "axisYChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstract3DAxis*"}], "name": "axisZChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "type", "type": "QAbstract3DGraph::ElementType"}], "name": "elementSelected", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "name": "orthoProjectionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "ratio", "type": "qreal"}], "name": "aspectRatioChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "ratio", "type": "qreal"}], "name": "horizontalAspectRatioChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "hints", "type": "QAbstract3DGraph::OptimizationHints"}], "name": "optimizationHintsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "name": "polarChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "offset", "type": "float"}], "name": "radialLabelOffsetChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "name": "reflectionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "reflectivity", "type": "qreal"}], "name": "reflectivityChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "locale", "type": "QLocale"}], "name": "localeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "data", "type": "QVector3D"}], "name": "queriedGraphPositionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "margin", "type": "qreal"}], "name": "marginChanged", "returnType": "void"}, {"access": "public", "name": "themeTypeChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "title", "type": "QString"}], "name": "handleAxisTitleChanged", "returnType": "void"}, {"access": "public", "name": "handleAxisLabelsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "min", "type": "float"}, {"name": "max", "type": "float"}], "name": "handleAxisRangeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "count", "type": "int"}], "name": "handleAxisSegmentCountChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "count", "type": "int"}], "name": "handleAxisSubSegmentCountChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "autoAdjust", "type": "bool"}], "name": "handleAxisAutoAdjustRangeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "format", "type": "QString"}], "name": "handleAxisLabelFormatChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enable", "type": "bool"}], "name": "handleAxisReversedChanged", "returnType": "void"}, {"access": "public", "name": "handleAxisFormatterDirty", "returnType": "void"}, {"access": "public", "arguments": [{"name": "angle", "type": "float"}], "name": "handleAxisLabelAutoRotationChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "visible", "type": "bool"}], "name": "handleAxisTitleVisibilityChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "fixed", "type": "bool"}], "name": "handleAxisTitleFixedChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "view", "type": "QAbstract3DInputHandler::InputView"}], "name": "handleInputViewChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "position", "type": "QPoint"}], "name": "handleInputPositionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "visible", "type": "bool"}], "name": "handleSeriesVisibilityChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "style", "type": "Q3DTheme::ColorStyle"}], "name": "handleThemeColorStyleChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QList<QColor>"}], "name": "handleThemeBaseColorsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "gradient", "type": "QList<QLinearGradient>"}], "name": "handleThemeBaseGradientsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "name": "handleThemeSingleHighlightColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "gradient", "type": "QLinearGradient"}], "name": "handleThemeSingleHighlightGradientChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "name": "handleThemeMultiHighlightColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "gradient", "type": "QLinearGradient"}], "name": "handleThemeMultiHighlightGradientChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "theme", "type": "Q3DTheme::Theme"}], "name": "handleThemeTypeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "quality", "type": "QAbstract3DGraph::ShadowQuality"}], "name": "handleRequestShadowQuality", "returnType": "void"}, {"access": "public", "name": "updateCustomItem", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "abstract3dcontroller_p.h", "outputRevision": 68}, {"classes": [{"className": "AxisHelper", "object": true, "qualifiedClassName": "AxisHelper", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "axishelper_p.h", "outputRevision": 68}, {"classes": [{"className": "BarInstancing", "object": true, "qualifiedClassName": "BarInstancing", "superClasses": [{"access": "public", "name": "QQuick3DInstancing"}]}], "inputFile": "barinstancing_p.h", "outputRevision": 68}, {"classes": [{"className": "Bars3DController", "object": true, "qualifiedClassName": "Bars3DController", "signals": [{"access": "public", "arguments": [{"name": "series", "type": "QBar3DSeries*"}], "name": "primarySeriesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "series", "type": "QBar3DSeries*"}], "name": "selectedSeriesChanged", "returnType": "void"}], "slots": [{"access": "public", "name": "handleArrayReset", "returnType": "void"}, {"access": "public", "arguments": [{"name": "startIndex", "type": "int"}, {"name": "count", "type": "int"}], "name": "handleRowsAdded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "startIndex", "type": "int"}, {"name": "count", "type": "int"}], "name": "handleRowsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "startIndex", "type": "int"}, {"name": "count", "type": "int"}], "name": "handleRowsRemoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "startIndex", "type": "int"}, {"name": "count", "type": "int"}], "name": "handleRowsInserted", "returnType": "void"}, {"access": "public", "arguments": [{"name": "rowIndex", "type": "int"}, {"name": "columnIndex", "type": "int"}], "name": "handleItemChanged", "returnType": "void"}, {"access": "public", "name": "handleDataRowLabelsChanged", "returnType": "void"}, {"access": "public", "name": "handleDataColumnLabelsChanged", "returnType": "void"}, {"access": "public", "name": "handleRowColorsChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Abstract3DController"}]}], "inputFile": "bars3dcontroller_p.h", "outputRevision": 68}, {"classes": [{"className": "Q3DBars", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "multiSeriesUniform", "notify": "multiSeriesUniformChanged", "read": "isMultiSeriesUniform", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setMultiSeriesUniform"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "barThickness", "notify": "barThicknessChanged", "read": "barThickness", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setBarThickness"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "barSpacing", "notify": "barSpacingChanged", "read": "barSpacing", "required": false, "scriptable": true, "stored": true, "type": "QSizeF", "user": false, "write": "setBarSpacing"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "barSpacingRelative", "notify": "barSpacingRelativeChanged", "read": "isBarSpacingRelative", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setBarSpacingRelative"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "barSeriesMargin", "notify": "barSeriesMarginChanged", "read": "barSeriesMargin", "required": false, "scriptable": true, "stored": true, "type": "QSizeF", "user": false, "write": "setBarSeriesMargin"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "rowAxis", "notify": "rowAxisChanged", "read": "rowAxis", "required": false, "scriptable": true, "stored": true, "type": "QCategory3DAxis*", "user": false, "write": "setRowAxis"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "columnAxis", "notify": "columnAxisChanged", "read": "columnAxis", "required": false, "scriptable": true, "stored": true, "type": "QCategory3DAxis*", "user": false, "write": "setColumnAxis"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "valueAxis", "notify": "valueAxisChanged", "read": "valueAxis", "required": false, "scriptable": true, "stored": true, "type": "QValue3DAxis*", "user": false, "write": "setValueAxis"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "primarySeries", "notify": "primarySeriesChanged", "read": "primarySeries", "required": false, "scriptable": true, "stored": true, "type": "QBar3DSeries*", "user": false, "write": "setPrimarySeries"}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "selectedSeries", "notify": "selectedSeriesChanged", "read": "selectedSeries", "required": false, "scriptable": true, "stored": true, "type": "QBar3DSeries*", "user": false}, {"constant": false, "designable": true, "final": false, "index": 10, "name": "floorLevel", "notify": "floorLevelChanged", "read": "floorLevel", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setFloorLevel"}], "qualifiedClassName": "Q3DBars", "signals": [{"access": "public", "arguments": [{"name": "uniform", "type": "bool"}], "name": "multiSeriesUniformChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "thicknessRatio", "type": "float"}], "name": "barThicknessChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "spacing", "type": "QSizeF"}], "name": "barSpacingChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "relative", "type": "bool"}], "name": "barSpacingRelativeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "margin", "type": "QSizeF"}], "name": "barSeriesMarginChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QCategory3DAxis*"}], "name": "rowAxisChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QCategory3DAxis*"}], "name": "columnAxisChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QValue3DAxis*"}], "name": "valueAxisChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "series", "type": "QBar3DSeries*"}], "name": "primarySeriesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "series", "type": "QBar3DSeries*"}], "name": "selectedSeriesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "level", "type": "float"}], "name": "floorLevelChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstract3DGraph"}]}], "inputFile": "q3dbars.h", "outputRevision": 68}, {"classes": [{"className": "Q3DCamera", "enums": [{"isClass": false, "isFlag": false, "name": "CameraPreset", "values": ["CameraPresetNone", "CameraPresetFrontLow", "CameraPresetFront", "CameraPresetFrontHigh", "CameraPresetLeftLow", "CameraPresetLeft", "CameraPresetLeftHigh", "CameraPresetRightLow", "CameraPresetRight", "CameraPresetRightHigh", "CameraPresetBehindLow", "CameraPresetBehind", "CameraPresetBehindHigh", "CameraPresetIsometricLeft", "CameraPresetIsometricLeftHigh", "CameraPresetIsometricRight", "CameraPresetIsometricRightHigh", "CameraPresetDirectlyAbove", "CameraPresetDirectlyAboveCW45", "CameraPresetDirectlyAboveCCW45", "CameraPresetFrontBelow", "CameraPresetLeftBelow", "CameraPresetRightBelow", "CameraPresetBehindBelow", "CameraPresetDirectlyBelow"]}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "xRotation", "notify": "xRotationChanged", "read": "xRotation", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setXRotation"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "yRotation", "notify": "yRotationChanged", "read": "yRotation", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setYRotation"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "zoomLevel", "notify": "zoomLevelChanged", "read": "zoomLevel", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setZoomLevel"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "cameraPreset", "notify": "cameraPresetChanged", "read": "cameraPreset", "required": false, "scriptable": true, "stored": true, "type": "Q3DCamera::CameraPreset", "user": false, "write": "setCameraPreset"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "wrapXRotation", "notify": "wrapXRotationChanged", "read": "wrapXRotation", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setWrapXRotation"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "wrapYRotation", "notify": "wrapYRotationChanged", "read": "wrapYRotation", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setWrapYRotation"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "target", "notify": "targetChanged", "read": "target", "required": false, "scriptable": true, "stored": true, "type": "QVector3D", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "minZoomLevel", "notify": "minZoomLevelChanged", "read": "minZoomLevel", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setMinZoomLevel"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "maxZoomLevel", "notify": "maxZoomLevelChanged", "read": "maxZoomLevel", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setMaxZoomLevel"}], "qualifiedClassName": "Q3DCamera", "signals": [{"access": "public", "arguments": [{"name": "rotation", "type": "float"}], "name": "xRotationChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "rotation", "type": "float"}], "name": "yRotationChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "zoomLevel", "type": "float"}], "name": "zoomLevelChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "preset", "type": "Q3DCamera::CameraPreset"}], "name": "cameraPresetChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "isEnabled", "type": "bool"}], "name": "wrapXRotationChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "isEnabled", "type": "bool"}], "name": "wrapYRotationChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "target", "type": "QVector3D"}], "name": "targetChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "zoomLevel", "type": "float"}], "name": "minZoomLevelChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "zoomLevel", "type": "float"}], "name": "maxZoomLevelChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "rotation", "type": "float"}], "name": "minXRotationChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "rotation", "type": "float"}], "name": "minYRotationChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "rotation", "type": "float"}], "name": "maxXRotationChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "rotation", "type": "float"}], "name": "maxYRotationChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "viewMatrix", "type": "QMatrix4x4"}], "name": "viewMatrixChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "name": "viewMatrixAutoUpdateChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Q3DObject"}]}], "inputFile": "q3dcamera.h", "outputRevision": 68}, {"classes": [{"className": "Q3DLight", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "autoPosition", "notify": "autoPositionChanged", "read": "isAutoPosition", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAutoPosition"}], "qualifiedClassName": "Q3DLight", "signals": [{"access": "public", "arguments": [{"name": "autoPosition", "type": "bool"}], "name": "autoPositionChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Q3DObject"}]}], "inputFile": "q3dlight.h", "outputRevision": 68}, {"classes": [{"className": "Q3DObject", "object": true, "properties": [{"constant": true, "designable": true, "final": false, "index": 0, "name": "parentScene", "read": "parentScene", "required": false, "scriptable": true, "stored": true, "type": "Q3DScene*", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "position", "notify": "positionChanged", "read": "position", "required": false, "scriptable": true, "stored": true, "type": "QVector3D", "user": false, "write": "setPosition"}], "qualifiedClassName": "Q3DObject", "signals": [{"access": "public", "arguments": [{"name": "position", "type": "QVector3D"}], "name": "positionChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "q3dobject.h", "outputRevision": 68}, {"classes": [{"className": "Q3DScatter", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "axisX", "notify": "axisXChanged", "read": "axisX", "required": false, "scriptable": true, "stored": true, "type": "QValue3DAxis*", "user": false, "write": "setAxisX"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "axisY", "notify": "axisYChanged", "read": "axisY", "required": false, "scriptable": true, "stored": true, "type": "QValue3DAxis*", "user": false, "write": "setAxisY"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "axisZ", "notify": "axisZChanged", "read": "axisZ", "required": false, "scriptable": true, "stored": true, "type": "QValue3DAxis*", "user": false, "write": "setAxisZ"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "selectedSeries", "notify": "selectedSeriesChanged", "read": "selectedSeries", "required": false, "scriptable": true, "stored": true, "type": "QScatter3DSeries*", "user": false}], "qualifiedClassName": "Q3DScatter", "signals": [{"access": "public", "arguments": [{"name": "axis", "type": "QValue3DAxis*"}], "name": "axisXChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QValue3DAxis*"}], "name": "axisYChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QValue3DAxis*"}], "name": "axisZChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "series", "type": "QScatter3DSeries*"}], "name": "selectedSeriesChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstract3DGraph"}]}], "inputFile": "q3dscatter.h", "outputRevision": 68}, {"classes": [{"className": "Q3DScene", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "viewport", "notify": "viewportChanged", "read": "viewport", "required": false, "scriptable": true, "stored": true, "type": "QRect", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "primarySubViewport", "notify": "primarySubViewportChanged", "read": "primarySubViewport", "required": false, "scriptable": true, "stored": true, "type": "QRect", "user": false, "write": "setPrimarySubViewport"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "secondarySubViewport", "notify": "secondarySubViewportChanged", "read": "secondarySubViewport", "required": false, "scriptable": true, "stored": true, "type": "QRect", "user": false, "write": "setSecondarySubViewport"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "selectionQueryPosition", "notify": "selectionQueryPositionChanged", "read": "selectionQueryPosition", "required": false, "scriptable": true, "stored": true, "type": "QPoint", "user": false, "write": "setSelectionQueryPosition"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "secondarySubviewOnTop", "notify": "secondarySubviewOnTopChanged", "read": "isSecondarySubviewOnTop", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setSecondarySubviewOnTop"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "slicingActive", "notify": "slicingActiveChanged", "read": "isSlicingActive", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setSlicingActive"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "activeCamera", "notify": "activeCameraChanged", "read": "activeCamera", "required": false, "scriptable": true, "stored": true, "type": "Q3DCamera*", "user": false, "write": "setActiveCamera"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "activeLight", "notify": "activeLightChanged", "read": "activeLight", "required": false, "scriptable": true, "stored": true, "type": "Q3DLight*", "user": false, "write": "setActiveLight"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "devicePixelRatio", "notify": "devicePixelRatioChanged", "read": "devicePixelRatio", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setDevicePixelRatio"}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "graphPositionQuery", "notify": "graphPositionQueryChanged", "read": "graphPositionQuery", "required": false, "scriptable": true, "stored": true, "type": "QPoint", "user": false, "write": "setGraphPositionQuery"}], "qualifiedClassName": "Q3DScene", "signals": [{"access": "public", "arguments": [{"name": "viewport", "type": "QRect"}], "name": "viewportChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "subViewport", "type": "QRect"}], "name": "primarySubViewportChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "subViewport", "type": "QRect"}], "name": "secondarySubViewportChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "isSecondaryOnTop", "type": "bool"}], "name": "secondarySubviewOnTopChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "isSlicingActive", "type": "bool"}], "name": "slicingActiveChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "camera", "type": "Q3DCamera*"}], "name": "activeCameraChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "light", "type": "Q3DLight*"}], "name": "activeLightChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "pixelRatio", "type": "float"}], "name": "devicePixelRatioChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "position", "type": "QPoint"}], "name": "selectionQueryPositionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "position", "type": "QPoint"}], "name": "graphPositionQueryChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "q3dscene.h", "outputRevision": 68}, {"classes": [{"className": "Q3DScenePrivate", "object": true, "qualifiedClassName": "Q3DScenePrivate", "signals": [{"access": "public", "name": "needRender", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "q3dscene_p.h", "outputRevision": 68}, {"classes": [{"className": "Q3DSurface", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "axisX", "notify": "axisXChanged", "read": "axisX", "required": false, "scriptable": true, "stored": true, "type": "QValue3DAxis*", "user": false, "write": "setAxisX"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "axisY", "notify": "axisYChanged", "read": "axisY", "required": false, "scriptable": true, "stored": true, "type": "QValue3DAxis*", "user": false, "write": "setAxisY"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "axisZ", "notify": "axisZChanged", "read": "axisZ", "required": false, "scriptable": true, "stored": true, "type": "QValue3DAxis*", "user": false, "write": "setAxisZ"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "selectedSeries", "notify": "selectedSeriesChanged", "read": "selectedSeries", "required": false, "scriptable": true, "stored": true, "type": "QSurface3DSeries*", "user": false}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "flipHorizontalGrid", "notify": "flipHorizontalGridChanged", "read": "flipHorizontalGrid", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setFlipHorizontalGrid"}], "qualifiedClassName": "Q3DSurface", "signals": [{"access": "public", "arguments": [{"name": "axis", "type": "QValue3DAxis*"}], "name": "axisXChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QValue3DAxis*"}], "name": "axisYChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QValue3DAxis*"}], "name": "axisZChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "series", "type": "QSurface3DSeries*"}], "name": "selectedSeriesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "flip", "type": "bool"}], "name": "flipHorizontalGridChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstract3DGraph"}]}], "inputFile": "q3dsurface.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "AbstractGraph3D"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Trying to create uncreatable: AbstractGraph3D."}], "className": "QAbstract3DGraph", "enums": [{"isClass": false, "isFlag": false, "name": "SelectionFlag", "values": ["SelectionNone", "SelectionItem", "SelectionRow", "SelectionItemAndRow", "SelectionColumn", "SelectionItemAndColumn", "SelectionRowAndColumn", "SelectionItemRowAndColumn", "SelectionSlice", "SelectionMultiSeries"]}, {"alias": "SelectionFlag", "isClass": false, "isFlag": true, "name": "SelectionFlags", "values": ["SelectionNone", "SelectionItem", "SelectionRow", "SelectionItemAndRow", "SelectionColumn", "SelectionItemAndColumn", "SelectionRowAndColumn", "SelectionItemRowAndColumn", "SelectionSlice", "SelectionMultiSeries"]}, {"isClass": false, "isFlag": false, "name": "ShadowQuality", "values": ["ShadowQualityNone", "ShadowQualityLow", "ShadowQualityMedium", "ShadowQualityHigh", "ShadowQualitySoftLow", "ShadowQualitySoftMedium", "ShadowQualitySoftHigh"]}, {"isClass": false, "isFlag": false, "name": "ElementType", "values": ["ElementNone", "ElementSeries", "ElementAxisXLabel", "ElementAxisYLabel", "ElementAxisZLabel", "ElementCustomItem"]}, {"isClass": false, "isFlag": false, "name": "OptimizationHint", "values": ["OptimizationDefault", "OptimizationStatic", "OptimizationLegacy"]}, {"alias": "OptimizationHint", "isClass": false, "isFlag": true, "name": "OptimizationHints", "values": ["OptimizationDefault", "OptimizationStatic", "OptimizationLegacy"]}, {"isClass": false, "isFlag": false, "name": "RenderingMode", "values": ["RenderDirectToBackground", "RenderIndirect"]}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "activeInputHandler", "notify": "activeInputHandlerChanged", "read": "activeInputHandler", "required": false, "scriptable": true, "stored": true, "type": "QAbstract3DInputHandler*", "user": false, "write": "setActiveInputHandler"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "activeTheme", "notify": "activeThemeChanged", "read": "activeTheme", "required": false, "scriptable": true, "stored": true, "type": "Q3DTheme*", "user": false, "write": "setActiveTheme"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "selectionMode", "notify": "selectionMode<PERSON>hanged", "read": "selectionMode", "required": false, "scriptable": true, "stored": true, "type": "QAbstract3DGraph::SelectionFlags", "user": false, "write": "setSelectionMode"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "shadowQuality", "notify": "shadowQualityChanged", "read": "shadowQuality", "required": false, "scriptable": true, "stored": true, "type": "QAbstract3DGraph::ShadowQuality", "user": false, "write": "setShadowQuality"}, {"constant": true, "designable": true, "final": false, "index": 4, "name": "scene", "read": "scene", "required": false, "scriptable": true, "stored": true, "type": "Q3DScene*", "user": false}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "measureFps", "notify": "measureFpsChanged", "read": "measureFps", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setMeasureFps"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "currentFps", "notify": "currentFpsChanged", "read": "currentFps", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "orthoProjection", "notify": "orthoProjectionChanged", "read": "isOrthoProjection", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setOrthoProjection"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "selectedElement", "notify": "selected<PERSON><PERSON><PERSON><PERSON><PERSON>", "read": "selectedElement", "required": false, "scriptable": true, "stored": true, "type": "QAbstract3DGraph::ElementType", "user": false}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "aspectRatio", "notify": "aspectRatioChanged", "read": "aspectRatio", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setAspectRatio"}, {"constant": false, "designable": true, "final": false, "index": 10, "name": "optimizationHints", "notify": "optimizationHintsChanged", "read": "optimizationHints", "required": false, "scriptable": true, "stored": true, "type": "QAbstract3DGraph::OptimizationHints", "user": false, "write": "setOptimizationHints"}, {"constant": false, "designable": true, "final": false, "index": 11, "name": "polar", "notify": "polarChanged", "read": "isPolar", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setPolar"}, {"constant": false, "designable": true, "final": false, "index": 12, "name": "radialLabelOffset", "notify": "radialLabelOffsetChanged", "read": "radialLabelOffset", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setRadialLabelOffset"}, {"constant": false, "designable": true, "final": false, "index": 13, "name": "horizontalAspectRatio", "notify": "horizontalAspectRatioChanged", "read": "horizontalAspectRatio", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setHorizontalAspectRatio"}, {"constant": false, "designable": true, "final": false, "index": 14, "name": "reflection", "notify": "reflectionChanged", "read": "isReflection", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setReflection"}, {"constant": false, "designable": true, "final": false, "index": 15, "name": "reflectivity", "notify": "reflectivityChanged", "read": "reflectivity", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setReflectivity"}, {"constant": false, "designable": true, "final": false, "index": 16, "name": "locale", "notify": "localeChanged", "read": "locale", "required": false, "scriptable": true, "stored": true, "type": "QLocale", "user": false, "write": "setLocale"}, {"constant": false, "designable": true, "final": false, "index": 17, "name": "queriedGraphPosition", "notify": "queriedGraphPositionChanged", "read": "queriedGraphPosition", "required": false, "scriptable": true, "stored": true, "type": "QVector3D", "user": false}, {"constant": false, "designable": true, "final": false, "index": 18, "name": "margin", "notify": "marginChanged", "read": "margin", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}], "qualifiedClassName": "QAbstract3DGraph", "signals": [{"access": "public", "arguments": [{"name": "inputHandler", "type": "QAbstract3DInputHandler*"}], "name": "activeInputHandlerChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "activeTheme", "type": "Q3DTheme*"}], "name": "activeThemeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "quality", "type": "QAbstract3DGraph::ShadowQuality"}], "name": "shadowQualityChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "selectionMode", "type": "QAbstract3DGraph::SelectionFlags"}], "name": "selectionMode<PERSON>hanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "type", "type": "QAbstract3DGraph::ElementType"}], "name": "selected<PERSON><PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "name": "measureFpsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "fps", "type": "int"}], "name": "currentFpsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "name": "orthoProjectionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "ratio", "type": "qreal"}], "name": "aspectRatioChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "hints", "type": "QAbstract3DGraph::OptimizationHints"}], "name": "optimizationHintsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "name": "polarChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "offset", "type": "float"}], "name": "radialLabelOffsetChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "ratio", "type": "qreal"}], "name": "horizontalAspectRatioChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "name": "reflectionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "reflectivity", "type": "qreal"}], "name": "reflectivityChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "locale", "type": "QLocale"}], "name": "localeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "data", "type": "QVector3D"}], "name": "queriedGraphPositionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "margin", "type": "qreal"}], "name": "marginChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickWidget"}]}], "inputFile": "qabstract3dgraph.h", "outputRevision": 68}, {"classes": [{"className": "Scatter3DController", "object": true, "qualifiedClassName": "Scatter3DController", "signals": [{"access": "public", "arguments": [{"name": "series", "type": "QScatter3DSeries*"}], "name": "selectedSeriesChanged", "returnType": "void"}], "slots": [{"access": "public", "name": "handleArrayReset", "returnType": "void"}, {"access": "public", "arguments": [{"name": "startIndex", "type": "int"}, {"name": "count", "type": "int"}], "name": "handleItemsAdded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "startIndex", "type": "int"}, {"name": "count", "type": "int"}], "name": "handleItemsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "startIndex", "type": "int"}, {"name": "count", "type": "int"}], "name": "handleItemsRemoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "startIndex", "type": "int"}, {"name": "count", "type": "int"}], "name": "handleItemsInserted", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Abstract3DController"}]}], "inputFile": "scatter3dcontroller_p.h", "outputRevision": 68}, {"classes": [{"className": "ScatterInstancing", "object": true, "qualifiedClassName": "ScatterInstancing", "superClasses": [{"access": "public", "name": "QQuick3DInstancing"}]}], "inputFile": "scatterinstancing_p.h", "outputRevision": 68}, {"classes": [{"className": "Surface3DController", "object": true, "qualifiedClassName": "Surface3DController", "signals": [{"access": "public", "arguments": [{"name": "series", "type": "QSurface3DSeries*"}], "name": "selectedSeriesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "flip", "type": "bool"}], "name": "flipHorizontalGridChanged", "returnType": "void"}], "slots": [{"access": "public", "name": "handleArrayReset", "returnType": "void"}, {"access": "public", "arguments": [{"name": "startIndex", "type": "int"}, {"name": "count", "type": "int"}], "name": "handleRowsAdded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "startIndex", "type": "int"}, {"name": "count", "type": "int"}], "name": "handleRowsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "startIndex", "type": "int"}, {"name": "count", "type": "int"}], "name": "handleRowsRemoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "startIndex", "type": "int"}, {"name": "count", "type": "int"}], "name": "handleRowsInserted", "returnType": "void"}, {"access": "public", "arguments": [{"name": "rowIndex", "type": "int"}, {"name": "columnIndex", "type": "int"}], "name": "handleItemChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "supported", "type": "bool"}], "name": "handleFlatShadingSupportedChange", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Abstract3DController"}]}], "inputFile": "surface3dcontroller_p.h", "outputRevision": 68}, {"classes": [{"className": "SurfaceSelectionInstancing", "object": true, "qualifiedClassName": "SurfaceSelectionInstancing", "superClasses": [{"access": "public", "name": "QQuick3DInstancing"}]}], "inputFile": "surfaceselectioninstancing_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "auto"}], "className": "ColorGradientStop", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "position", "notify": "positionChanged", "read": "position", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setPosition"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "color", "notify": "colorChanged", "read": "color", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setColor"}], "qualifiedClassName": "ColorGradientStop", "signals": [{"access": "public", "arguments": [{"name": "position", "type": "qreal"}], "name": "positionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "name": "colorChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}, {"classInfos": [{"name": "DefaultProperty", "value": "stops"}, {"name": "QML.Element", "value": "auto"}], "className": "ColorGradient", "object": true, "properties": [{"constant": true, "designable": true, "final": false, "index": 0, "name": "stops", "read": "stops", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<ColorGradientStop>", "user": false}], "qualifiedClassName": "ColorGradient", "signals": [{"access": "public", "name": "updated", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "colorgradient_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "ThemeColor"}], "className": "DeclarativeColor", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "color", "notify": "colorChanged", "read": "color", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setColor"}], "qualifiedClassName": "DeclarativeColor", "signals": [{"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "name": "colorChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "declarativecolor_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "Scene3D"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Trying to create uncreatable: Scene3D."}], "className": "Declarative3DScene", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "selectionQueryPosition", "notify": "selectionQueryPositionChanged", "read": "selectionQueryPosition", "required": false, "scriptable": true, "stored": true, "type": "QPointF", "user": false, "write": "setSelectionQueryPosition"}, {"constant": true, "designable": true, "final": false, "index": 1, "name": "invalidSelectionPoint", "read": "invalidSelectionPoint", "required": false, "scriptable": true, "stored": true, "type": "QPoint", "user": false}], "qualifiedClassName": "Declarative3DScene", "signals": [{"access": "public", "arguments": [{"name": "position", "type": "QPointF"}], "name": "selectionQueryPositionChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Q3DScene"}]}], "inputFile": "declarativescene_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "DefaultProperty", "value": "seriesChildren"}, {"name": "QML.Element", "value": "Bar3DSeries"}], "className": "DeclarativeBar3DSeries", "object": true, "properties": [{"constant": true, "designable": true, "final": false, "index": 0, "name": "seriesChildren", "read": "seriesChildren", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<QObject>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "<PERSON><PERSON><PERSON>", "notify": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "read": "<PERSON><PERSON><PERSON>", "required": false, "scriptable": true, "stored": true, "type": "QPointF", "user": false, "write": "setSelectedBar"}, {"constant": true, "designable": true, "final": false, "index": 2, "name": "invalidSelectionPosition", "read": "invalidSelectionPosition", "required": false, "scriptable": true, "stored": true, "type": "QPointF", "user": false}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "baseGradient", "notify": "baseGradientChanged", "read": "baseGradient", "required": false, "scriptable": true, "stored": true, "type": "ColorGradient*", "user": false, "write": "setBaseGradient"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "singleHighlightGradient", "notify": "singleHighlightGradientChanged", "read": "singleHighlightGradient", "required": false, "scriptable": true, "stored": true, "type": "ColorGradient*", "user": false, "write": "setSingleHighlightGradient"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "multiHighlightGradient", "notify": "multiHighlightGradientChanged", "read": "multiHighlightGradient", "required": false, "scriptable": true, "stored": true, "type": "ColorGradient*", "user": false, "write": "setMultiHighlightGradient"}, {"constant": true, "designable": true, "final": false, "index": 6, "name": "rowColors", "read": "rowColors", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<DeclarativeColor>", "user": false}], "qualifiedClassName": "DeclarativeBar3DSeries", "signals": [{"access": "public", "arguments": [{"name": "position", "type": "QPointF"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "arguments": [{"name": "gradient", "type": "ColorGradient*"}], "name": "baseGradientChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "gradient", "type": "ColorGradient*"}], "name": "singleHighlightGradientChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "gradient", "type": "ColorGradient*"}], "name": "multiHighlightGradientChanged", "returnType": "void"}], "slots": [{"access": "public", "name": "handleBaseGradientUpdate", "returnType": "void"}, {"access": "public", "name": "handleSingleHighlightGradientUpdate", "returnType": "void"}, {"access": "public", "name": "handleMultiHighlightGradientUpdate", "returnType": "void"}, {"access": "public", "name": "handleRowColorUpdate", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QBar3DSeries"}]}, {"classInfos": [{"name": "DefaultProperty", "value": "seriesChildren"}, {"name": "QML.Element", "value": "Scatter3DSeries"}], "className": "DeclarativeScatter3DSeries", "object": true, "properties": [{"constant": true, "designable": true, "final": false, "index": 0, "name": "seriesChildren", "read": "seriesChildren", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<QObject>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "baseGradient", "notify": "baseGradientChanged", "read": "baseGradient", "required": false, "scriptable": true, "stored": true, "type": "ColorGradient*", "user": false, "write": "setBaseGradient"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "singleHighlightGradient", "notify": "singleHighlightGradientChanged", "read": "singleHighlightGradient", "required": false, "scriptable": true, "stored": true, "type": "ColorGradient*", "user": false, "write": "setSingleHighlightGradient"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "multiHighlightGradient", "notify": "multiHighlightGradientChanged", "read": "multiHighlightGradient", "required": false, "scriptable": true, "stored": true, "type": "ColorGradient*", "user": false, "write": "setMultiHighlightGradient"}, {"constant": true, "designable": true, "final": false, "index": 4, "name": "invalidSelectionIndex", "read": "invalidSelectionIndex", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}], "qualifiedClassName": "DeclarativeScatter3DSeries", "signals": [{"access": "public", "arguments": [{"name": "gradient", "type": "ColorGradient*"}], "name": "baseGradientChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "gradient", "type": "ColorGradient*"}], "name": "singleHighlightGradientChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "gradient", "type": "ColorGradient*"}], "name": "multiHighlightGradientChanged", "returnType": "void"}], "slots": [{"access": "public", "name": "handleBaseGradientUpdate", "returnType": "void"}, {"access": "public", "name": "handleSingleHighlightGradientUpdate", "returnType": "void"}, {"access": "public", "name": "handleMultiHighlightGradientUpdate", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QScatter3DSeries"}]}, {"classInfos": [{"name": "DefaultProperty", "value": "seriesChildren"}, {"name": "QML.Element", "value": "Surface3DSeries"}], "className": "DeclarativeSurface3DSeries", "object": true, "properties": [{"constant": true, "designable": true, "final": false, "index": 0, "name": "seriesChildren", "read": "seriesChildren", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<QObject>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "selectedPoint", "notify": "selectedPointChanged", "read": "selectedPoint", "required": false, "scriptable": true, "stored": true, "type": "QPointF", "user": false, "write": "setSelectedPoint"}, {"constant": true, "designable": true, "final": false, "index": 2, "name": "invalidSelectionPosition", "read": "invalidSelectionPosition", "required": false, "scriptable": true, "stored": true, "type": "QPointF", "user": false}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "baseGradient", "notify": "baseGradientChanged", "read": "baseGradient", "required": false, "scriptable": true, "stored": true, "type": "ColorGradient*", "user": false, "write": "setBaseGradient"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "singleHighlightGradient", "notify": "singleHighlightGradientChanged", "read": "singleHighlightGradient", "required": false, "scriptable": true, "stored": true, "type": "ColorGradient*", "user": false, "write": "setSingleHighlightGradient"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "multiHighlightGradient", "notify": "multiHighlightGradientChanged", "read": "multiHighlightGradient", "required": false, "scriptable": true, "stored": true, "type": "ColorGradient*", "user": false, "write": "setMultiHighlightGradient"}], "qualifiedClassName": "DeclarativeSurface3DSeries", "signals": [{"access": "public", "arguments": [{"name": "position", "type": "QPointF"}], "name": "selectedPointChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "gradient", "type": "ColorGradient*"}], "name": "baseGradientChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "gradient", "type": "ColorGradient*"}], "name": "singleHighlightGradientChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "gradient", "type": "ColorGradient*"}], "name": "multiHighlightGradientChanged", "returnType": "void"}], "slots": [{"access": "public", "name": "handleBaseGradientUpdate", "returnType": "void"}, {"access": "public", "name": "handleSingleHighlightGradientUpdate", "returnType": "void"}, {"access": "public", "name": "handleMultiHighlightGradientUpdate", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QSurface3DSeries"}]}], "inputFile": "declarativeseries_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "DefaultProperty", "value": "themeChildren"}, {"name": "QML.Element", "value": "Theme3D"}], "className": "DeclarativeTheme3D", "interfaces": [[{"className": "QQmlParserStatus", "id": "\"org.qt-project.Qt.QQmlParserStatus\""}]], "object": true, "properties": [{"constant": true, "designable": true, "final": false, "index": 0, "name": "themeChildren", "read": "themeChildren", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<QObject>", "user": false}, {"constant": true, "designable": true, "final": false, "index": 1, "name": "baseColors", "read": "baseColors", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<DeclarativeColor>", "user": false}, {"constant": true, "designable": true, "final": false, "index": 2, "name": "baseGradients", "read": "baseGradients", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<ColorGradient>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "singleHighlightGradient", "notify": "singleHighlightGradientChanged", "read": "singleHighlightGradient", "required": false, "scriptable": true, "stored": true, "type": "ColorGradient*", "user": false, "write": "setSingleHighlightGradient"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "multiHighlightGradient", "notify": "multiHighlightGradientChanged", "read": "multiHighlightGradient", "required": false, "scriptable": true, "stored": true, "type": "ColorGradient*", "user": false, "write": "setMultiHighlightGradient"}], "qualifiedClassName": "DeclarativeTheme3D", "signals": [{"access": "public", "arguments": [{"name": "gradient", "type": "ColorGradient*"}], "name": "singleHighlightGradientChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "gradient", "type": "ColorGradient*"}], "name": "multiHighlightGradientChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Q3DTheme"}, {"access": "public", "name": "QQmlParserStatus"}]}], "inputFile": "declarativetheme_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "anonymous"}, {"name": "QML.Foreign", "value": "Q3DScene"}], "className": "Q3DSceneForeign", "gadget": true, "qualifiedClassName": "Q3DSceneForeign"}, {"classInfos": [{"name": "QML.Element", "value": "Camera3D"}, {"name": "QML.Foreign", "value": "Q3DCamera"}, {"name": "QML.AddedInVersion", "value": "1542"}], "className": "Q3DCameraGraphsForeign", "gadget": true, "qualifiedClassName": "Q3DCameraGraphsForeign"}, {"classInfos": [{"name": "QML.Element", "value": "Light3D"}, {"name": "QML.Foreign", "value": "Q3DLight"}, {"name": "QML.AddedInVersion", "value": "1542"}], "className": "Q3DLightGraphsForeign", "gadget": true, "qualifiedClassName": "Q3DLightGraphsForeign"}, {"classInfos": [{"name": "QML.Element", "value": "CategoryAxis3D"}, {"name": "QML.Foreign", "value": "QCategory3DAxis"}, {"name": "QML.AddedInVersion", "value": "1542"}], "className": "QCategory3DAxisGraphsForeign", "gadget": true, "qualifiedClassName": "QCategory3DAxisGraphsForeign"}, {"classInfos": [{"name": "QML.Element", "value": "HeightMapSurfaceDataProxy"}, {"name": "QML.Foreign", "value": "QHeightMapSurfaceDataProxy"}, {"name": "QML.AddedInVersion", "value": "1542"}], "className": "QHeightMapSurfaceDataProxyGraphsForeign", "gadget": true, "qualifiedClassName": "QHeightMapSurfaceDataProxyGraphsForeign"}, {"classInfos": [{"name": "QML.Element", "value": "ItemModelBarDataProxy"}, {"name": "QML.Foreign", "value": "QItemModelBarDataProxy"}, {"name": "QML.AddedInVersion", "value": "1542"}], "className": "QItemModelBarDataProxyGraphsForeign", "gadget": true, "qualifiedClassName": "QItemModelBarDataProxyGraphsForeign"}, {"classInfos": [{"name": "QML.Element", "value": "ItemModelScatterDataProxy"}, {"name": "QML.Foreign", "value": "QItemModelScatterDataProxy"}, {"name": "QML.AddedInVersion", "value": "1542"}], "className": "QItemModelScatterDataProxyGraphsForeign", "gadget": true, "qualifiedClassName": "QItemModelScatterDataProxyGraphsForeign"}, {"classInfos": [{"name": "QML.Element", "value": "ItemModelSurfaceDataProxy"}, {"name": "QML.Foreign", "value": "QItemModelSurfaceDataProxy"}, {"name": "QML.AddedInVersion", "value": "1542"}], "className": "QItemModelSurfaceDataProxyGraphsForeign", "gadget": true, "qualifiedClassName": "QItemModelSurfaceDataProxyGraphsForeign"}, {"classInfos": [{"name": "QML.Element", "value": "ValueAxis3D"}, {"name": "QML.Foreign", "value": "QValue3DAxis"}, {"name": "QML.AddedInVersion", "value": "1542"}], "className": "QValue3DAxisGraphsForeign", "gadget": true, "qualifiedClassName": "QValue3DAxisGraphsForeign"}, {"classInfos": [{"name": "QML.Element", "value": "Custom3DItem"}, {"name": "QML.Foreign", "value": "QCustom3DItem"}, {"name": "QML.AddedInVersion", "value": "1542"}], "className": "QCustom3DItemGraphsForeign", "gadget": true, "qualifiedClassName": "QCustom3DItemGraphsForeign"}, {"classInfos": [{"name": "QML.Element", "value": "Custom3DLabel"}, {"name": "QML.Foreign", "value": "QCustom3DLabel"}, {"name": "QML.AddedInVersion", "value": "1542"}], "className": "QCustom3DLabelGraphsForeign", "gadget": true, "qualifiedClassName": "QCustom3DLabelGraphsForeign"}, {"classInfos": [{"name": "QML.Element", "value": "LogValueAxis3DFormatter"}, {"name": "QML.Foreign", "value": "QLogValue3DAxisFormatter"}, {"name": "QML.AddedInVersion", "value": "1542"}], "className": "QLogValue3DAxisFormatterGraphsForeign", "gadget": true, "qualifiedClassName": "QLogValue3DAxisFormatterGraphsForeign"}, {"classInfos": [{"name": "QML.Element", "value": "ValueAxis3DFormatter"}, {"name": "QML.Foreign", "value": "QValue3DAxisFormatter"}, {"name": "QML.AddedInVersion", "value": "1542"}], "className": "QValue3DAxisFormatterGraphsForeign", "gadget": true, "qualifiedClassName": "QValue3DAxisFormatterGraphsForeign"}, {"classInfos": [{"name": "QML.Element", "value": "InputHandler3D"}, {"name": "QML.Foreign", "value": "Q3DInputHandler"}, {"name": "QML.AddedInVersion", "value": "1542"}], "className": "Q3DInputHandlerGraphsForeign", "gadget": true, "qualifiedClassName": "Q3DInputHandlerGraphsForeign"}, {"classInfos": [{"name": "QML.Element", "value": "Custom3DVolume"}, {"name": "QML.Foreign", "value": "QCustom3DVolume"}, {"name": "QML.AddedInVersion", "value": "1542"}], "className": "QCustom3DVolumeGraphsForeign", "gadget": true, "qualifiedClassName": "QCustom3DVolumeGraphsForeign"}, {"classInfos": [{"name": "QML.Element", "value": "TouchInputHandler3D"}, {"name": "QML.Foreign", "value": "QTouch3DInputHandler"}, {"name": "QML.AddedInVersion", "value": "1542"}], "className": "QTouch3DInputHandlerGraphsForeign", "gadget": true, "qualifiedClassName": "QTouch3DInputHandlerGraphsForeign"}, {"classInfos": [{"name": "QML.Element", "value": "Q3DTheme"}, {"name": "QML.Foreign", "value": "Q3DTheme"}, {"name": "QML.AddedInVersion", "value": "1542"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Trying to create uncreatable: Q3DTheme, use Theme3D instead."}], "className": "Q3DThemeGraphsForeign", "gadget": true, "qualifiedClassName": "Q3DThemeGraphsForeign"}, {"classInfos": [{"name": "QML.Element", "value": "QBar3DSeries"}, {"name": "QML.Foreign", "value": "QBar3DSeries"}, {"name": "QML.AddedInVersion", "value": "1542"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Trying to create uncreatable: QBar3DSeries, use Bar3DSeries instead."}], "className": "QBar3DSeriesGraphsForeign", "gadget": true, "qualifiedClassName": "QBar3DSeriesGraphsForeign"}, {"classInfos": [{"name": "QML.Element", "value": "QScatter3DSeries"}, {"name": "QML.Foreign", "value": "QScatter3DSeries"}, {"name": "QML.AddedInVersion", "value": "1542"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Trying to create uncreatable: QScatter3DSeries, use Scatter3DSeries instead."}], "className": "QScatter3DSeriesGraphsForeign", "gadget": true, "qualifiedClassName": "QScatter3DSeriesGraphsForeign"}, {"classInfos": [{"name": "QML.Element", "value": "QSurface3DSeries"}, {"name": "QML.Foreign", "value": "QSurface3DSeries"}, {"name": "QML.AddedInVersion", "value": "1542"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Trying to create uncreatable: QSurface3DSeries, use Surface3DSeries instead."}], "className": "QSurface3DSeriesGraphsForeign", "gadget": true, "qualifiedClassName": "QSurface3DSeriesGraphsForeign"}, {"classInfos": [{"name": "QML.Element", "value": "Object3D"}, {"name": "QML.Foreign", "value": "Q3DObject"}, {"name": "QML.AddedInVersion", "value": "1542"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Trying to create uncreatable: Object3D."}], "className": "Q3DObjectGraphsForeign", "gadget": true, "qualifiedClassName": "Q3DObjectGraphsForeign"}, {"classInfos": [{"name": "QML.Element", "value": "AbstractAxis3D"}, {"name": "QML.Foreign", "value": "QAbstract3DAxis"}, {"name": "QML.AddedInVersion", "value": "1542"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Trying to create uncreatable: AbstractAxis3D."}], "className": "QAbstract3DAxisGraphsForeign", "gadget": true, "qualifiedClassName": "QAbstract3DAxisGraphsForeign"}, {"classInfos": [{"name": "QML.Element", "value": "AbstractInputHandler3D"}, {"name": "QML.Foreign", "value": "QAbstract3DInputHandler"}, {"name": "QML.AddedInVersion", "value": "1542"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Trying to create uncreatable: AbstractInputHandler3D."}], "className": "QAbstract3DInputHandlerGraphsForeign", "gadget": true, "qualifiedClassName": "QAbstract3DInputHandlerGraphsForeign"}, {"classInfos": [{"name": "QML.Element", "value": "Abstract3DSeries"}, {"name": "QML.Foreign", "value": "QAbstract3DSeries"}, {"name": "QML.AddedInVersion", "value": "1542"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Trying to create uncreatable: Abstract3DSeries."}], "className": "QAbstract3DSeriesGraphsForeign", "gadget": true, "qualifiedClassName": "QAbstract3DSeriesGraphsForeign"}, {"classInfos": [{"name": "QML.Element", "value": "AbstractDataProxy"}, {"name": "QML.Foreign", "value": "QAbstractDataProxy"}, {"name": "QML.AddedInVersion", "value": "1542"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Trying to create uncreatable: AbstractDataProxy."}], "className": "QAbstractDataProxyGraphsForeign", "gadget": true, "qualifiedClassName": "QAbstractDataProxyGraphsForeign"}, {"classInfos": [{"name": "QML.Element", "value": "AbstractItemModel"}, {"name": "QML.Foreign", "value": "QAbstractItemModel"}, {"name": "QML.AddedInVersion", "value": "1542"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Trying to create uncreatable: AbstractItemModel."}], "className": "QAbstractItemModelGraphsForeign", "gadget": true, "qualifiedClassName": "QAbstractItemModelGraphsForeign"}, {"classInfos": [{"name": "QML.Element", "value": "BarDataProxy"}, {"name": "QML.Foreign", "value": "QBarDataProxy"}, {"name": "QML.AddedInVersion", "value": "1542"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Trying to create uncreatable: BarDataProxy."}], "className": "QBarDataProxyGraphsForeign", "gadget": true, "qualifiedClassName": "QBarDataProxyGraphsForeign"}, {"classInfos": [{"name": "QML.Element", "value": "ScatterDataProxy"}, {"name": "QML.Foreign", "value": "QScatterDataProxy"}, {"name": "QML.AddedInVersion", "value": "1542"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Trying to create uncreatable: ScatterDataProxy."}], "className": "QScatterDataProxyGraphsForeign", "gadget": true, "qualifiedClassName": "QScatterDataProxyGraphsForeign"}, {"classInfos": [{"name": "QML.Element", "value": "SurfaceDataProxy"}, {"name": "QML.Foreign", "value": "QSurfaceDataProxy"}, {"name": "QML.AddedInVersion", "value": "1542"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Trying to create uncreatable: SurfaceDataProxy."}], "className": "QSurfaceDataProxyGraphsForeign", "gadget": true, "qualifiedClassName": "QSurfaceDataProxyGraphsForeign"}], "inputFile": "foreigntypes_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "DefaultProperty", "value": "seriesList"}, {"name": "QML.Element", "value": "Bars3D"}], "className": "QQuickGraphsBars", "methods": [{"access": "public", "arguments": [{"name": "series", "type": "QBar3DSeries*"}], "name": "addSeries", "returnType": "void"}, {"access": "public", "arguments": [{"name": "series", "type": "QBar3DSeries*"}], "name": "removeSeries", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "series", "type": "QBar3DSeries*"}], "name": "insertSeries", "returnType": "void"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "rowAxis", "notify": "rowAxisChanged", "read": "rowAxis", "required": false, "scriptable": true, "stored": true, "type": "QCategory3DAxis*", "user": false, "write": "setRowAxis"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "valueAxis", "notify": "valueAxisChanged", "read": "valueAxis", "required": false, "scriptable": true, "stored": true, "type": "QValue3DAxis*", "user": false, "write": "setValueAxis"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "columnAxis", "notify": "columnAxisChanged", "read": "columnAxis", "required": false, "scriptable": true, "stored": true, "type": "QCategory3DAxis*", "user": false, "write": "setColumnAxis"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "multiSeriesUniform", "notify": "multiSeriesUniformChanged", "read": "isMultiSeriesUniform", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setMultiSeriesUniform"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "barThickness", "notify": "barThicknessChanged", "read": "barThickness", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setBarThickness"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "barSpacing", "notify": "barSpacingChanged", "read": "barSpacing", "required": false, "scriptable": true, "stored": true, "type": "QSizeF", "user": false, "write": "setBarSpacing"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "barSpacingRelative", "notify": "barSpacingRelativeChanged", "read": "isBarSpacingRelative", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setBarSpacingRelative"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "barSeriesMargin", "notify": "barSeriesMarginChanged", "read": "barSeriesMargin", "required": false, "scriptable": true, "stored": true, "type": "QSizeF", "user": false, "write": "setBarSeriesMargin"}, {"constant": true, "designable": true, "final": false, "index": 8, "name": "seriesList", "read": "seriesList", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<QBar3DSeries>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "selectedSeries", "notify": "selectedSeriesChanged", "read": "selectedSeries", "required": false, "scriptable": true, "stored": true, "type": "QBar3DSeries*", "user": false}, {"constant": false, "designable": true, "final": false, "index": 10, "name": "primarySeries", "notify": "primarySeriesChanged", "read": "primarySeries", "required": false, "scriptable": true, "stored": true, "type": "QBar3DSeries*", "user": false, "write": "setPrimarySeries"}, {"constant": false, "designable": true, "final": false, "index": 11, "name": "floorLevel", "notify": "floorLevelChanged", "read": "floorLevel", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setFloorLevel"}], "qualifiedClassName": "QQuickGraphsBars", "signals": [{"access": "public", "arguments": [{"name": "axis", "type": "QCategory3DAxis*"}], "name": "rowAxisChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QValue3DAxis*"}], "name": "valueAxisChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QCategory3DAxis*"}], "name": "columnAxisChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "uniform", "type": "bool"}], "name": "multiSeriesUniformChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "thicknessRatio", "type": "float"}], "name": "barThicknessChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "spacing", "type": "QSizeF"}], "name": "barSpacingChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "relative", "type": "bool"}], "name": "barSpacingRelativeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "margin", "type": "QSizeF"}], "name": "barSeriesMarginChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "filename", "type": "QString"}], "name": "meshFileNameChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "series", "type": "QBar3DSeries*"}], "name": "primarySeriesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "series", "type": "QBar3DSeries*"}], "name": "selectedSeriesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "level", "type": "float"}], "name": "floorLevelChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "axis", "type": "QAbstract3DAxis*"}], "name": "handleAxisXChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstract3DAxis*"}], "name": "handleAxisYChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstract3DAxis*"}], "name": "handleAxisZChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "mesh", "type": "QAbstract3DSeries::Mesh"}], "name": "handleSeriesMeshChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enable", "type": "bool"}], "name": "handleMeshSmoothChanged", "returnType": "void"}, {"access": "public", "name": "handleRowCountChanged", "returnType": "void"}, {"access": "public", "name": "handleColCountChanged", "returnType": "void"}, {"access": "public", "name": "handleRowColorsChanged", "returnType": "void"}, {"access": "public", "name": "handleCameraRotationChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickGraphsItem"}]}], "inputFile": "qquickgraphsbars_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "GraphsItem3D"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Trying to create uncreatable: GraphsItem3D."}], "className": "QQuickGraphsItem", "methods": [{"access": "public", "name": "clearSelection", "returnType": "void"}, {"access": "public", "arguments": [{"name": "series", "type": "QAbstract3DSeries*"}], "name": "hasSeries", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "item", "type": "QCustom3DItem*"}], "name": "addCustomItem", "returnType": "int"}, {"access": "public", "name": "removeCustomItems", "returnType": "void"}, {"access": "public", "arguments": [{"name": "item", "type": "QCustom3DItem*"}], "name": "removeCustomItem", "returnType": "void"}, {"access": "public", "arguments": [{"name": "position", "type": "QVector3D"}], "name": "removeCustomItemAt", "returnType": "void"}, {"access": "public", "arguments": [{"name": "item", "type": "QCustom3DItem*"}], "name": "releaseCustomItem", "returnType": "void"}, {"access": "public", "name": "selectedLabelIndex", "returnType": "int"}, {"access": "public", "name": "<PERSON><PERSON><PERSON><PERSON>", "returnType": "QAbstract3DAxis*"}, {"access": "public", "name": "selectedCustomItemIndex", "returnType": "int"}, {"access": "public", "name": "selectedCustomItem", "returnType": "QCustom3DItem*"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "selectionMode", "notify": "selectionMode<PERSON>hanged", "read": "selectionMode", "required": false, "scriptable": true, "stored": true, "type": "QAbstract3DGraph::SelectionFlags", "user": false, "write": "setSelectionMode"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "shadowQuality", "notify": "shadowQualityChanged", "read": "shadowQuality", "required": false, "scriptable": true, "stored": true, "type": "QAbstract3DGraph::ShadowQuality", "user": false, "write": "setShadowQuality"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "msaaSamples", "notify": "msaaSamplesChanged", "read": "msaaSamples", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setMsaaSamples"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "scene", "notify": "sceneChanged", "read": "scene", "required": false, "scriptable": true, "stored": true, "type": "Declarative3DScene*", "user": false}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "inputHandler", "notify": "inputHandlerChanged", "read": "inputHandler", "required": false, "scriptable": true, "stored": true, "type": "QAbstract3DInputHandler*", "user": false, "write": "setInputHandler"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "theme", "notify": "themeChanged", "read": "theme", "required": false, "scriptable": true, "stored": true, "type": "Q3DTheme*", "user": false, "write": "setTheme"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "renderingMode", "notify": "renderingModeChanged", "read": "renderingMode", "required": false, "scriptable": true, "stored": true, "type": "QAbstract3DGraph::RenderingMode", "user": false, "write": "setRenderingMode"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "measureFps", "notify": "measureFpsChanged", "read": "measureFps", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setMeasureFps"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "currentFps", "notify": "currentFpsChanged", "read": "currentFps", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": true, "designable": true, "final": false, "index": 9, "name": "customItemList", "read": "customItemList", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<QCustom3DItem>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 10, "name": "orthoProjection", "notify": "orthoProjectionChanged", "read": "isOrthoProjection", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setOrthoProjection"}, {"constant": false, "designable": true, "final": false, "index": 11, "name": "selectedElement", "notify": "selected<PERSON><PERSON><PERSON><PERSON><PERSON>", "read": "selectedElement", "required": false, "scriptable": true, "stored": true, "type": "QAbstract3DGraph::ElementType", "user": false}, {"constant": false, "designable": true, "final": false, "index": 12, "name": "aspectRatio", "notify": "aspectRatioChanged", "read": "aspectRatio", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setAspectRatio"}, {"constant": false, "designable": true, "final": false, "index": 13, "name": "optimizationHints", "notify": "optimizationHintsChanged", "read": "optimizationHints", "required": false, "scriptable": true, "stored": true, "type": "QAbstract3DGraph::OptimizationHints", "user": false, "write": "setOptimizationHints"}, {"constant": false, "designable": true, "final": false, "index": 14, "name": "polar", "notify": "polarChanged", "read": "isPolar", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setPolar"}, {"constant": false, "designable": true, "final": false, "index": 15, "name": "radialLabelOffset", "notify": "radialLabelOffsetChanged", "read": "radialLabelOffset", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setRadialLabelOffset"}, {"constant": false, "designable": true, "final": false, "index": 16, "name": "horizontalAspectRatio", "notify": "horizontalAspectRatioChanged", "read": "horizontalAspectRatio", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setHorizontalAspectRatio"}, {"constant": false, "designable": true, "final": false, "index": 17, "name": "reflection", "notify": "reflectionChanged", "read": "isReflection", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setReflection"}, {"constant": false, "designable": true, "final": false, "index": 18, "name": "reflectivity", "notify": "reflectivityChanged", "read": "reflectivity", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setReflectivity"}, {"constant": false, "designable": true, "final": false, "index": 19, "name": "locale", "notify": "localeChanged", "read": "locale", "required": false, "scriptable": true, "stored": true, "type": "QLocale", "user": false, "write": "setLocale"}, {"constant": false, "designable": true, "final": false, "index": 20, "name": "queriedGraphPosition", "notify": "queriedGraphPositionChanged", "read": "queriedGraphPosition", "required": false, "scriptable": true, "stored": true, "type": "QVector3D", "user": false}, {"constant": false, "designable": true, "final": false, "index": 21, "name": "margin", "notify": "marginChanged", "read": "margin", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}], "qualifiedClassName": "QQuickGraphsItem", "signals": [{"access": "public", "arguments": [{"name": "mode", "type": "QAbstract3DGraph::SelectionFlags"}], "name": "selectionMode<PERSON>hanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "quality", "type": "QAbstract3DGraph::ShadowQuality"}], "name": "shadowQualityChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "supported", "type": "bool"}], "name": "shadowsSupportedChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "samples", "type": "int"}], "name": "msaaSamplesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "inputHandler", "type": "QAbstract3DInputHandler*"}], "name": "inputHandlerChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "theme", "type": "Q3DTheme*"}], "name": "themeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "mode", "type": "QAbstract3DGraph::RenderingMode"}], "name": "renderingModeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "name": "measureFpsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "fps", "type": "int"}], "name": "currentFpsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "type", "type": "QAbstract3DGraph::ElementType"}], "name": "selected<PERSON><PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "name": "orthoProjectionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "ratio", "type": "qreal"}], "name": "aspectRatioChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "hints", "type": "QAbstract3DGraph::OptimizationHints"}], "name": "optimizationHintsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "name": "polarChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "offset", "type": "float"}], "name": "radialLabelOffsetChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "ratio", "type": "qreal"}], "name": "horizontalAspectRatioChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "name": "reflectionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "reflectivity", "type": "qreal"}], "name": "reflectivityChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "locale", "type": "QLocale"}], "name": "localeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "data", "type": "QVector3D"}], "name": "queriedGraphPositionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "margin", "type": "qreal"}], "name": "marginChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "axis", "type": "QAbstract3DAxis*"}], "name": "handleAxisXChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstract3DAxis*"}], "name": "handleAxisYChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstract3DAxis*"}], "name": "handleAxisZChanged", "returnType": "void"}, {"access": "public", "name": "handleFpsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "obj", "type": "QObject*"}], "name": "windowDestroyed", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuick3DViewport"}]}], "inputFile": "qquickgraphsitem_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "DefaultProperty", "value": "seriesList"}, {"name": "QML.Element", "value": "Scatter3D"}], "className": "QQuickGraphsScatter", "methods": [{"access": "public", "arguments": [{"name": "series", "type": "QScatter3DSeries*"}], "name": "addSeries", "returnType": "void"}, {"access": "public", "arguments": [{"name": "series", "type": "QScatter3DSeries*"}], "name": "removeSeries", "returnType": "void"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "axisX", "notify": "axisXChanged", "read": "axisX", "required": false, "scriptable": true, "stored": true, "type": "QValue3DAxis*", "user": false, "write": "setAxisX"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "axisY", "notify": "axisYChanged", "read": "axisY", "required": false, "scriptable": true, "stored": true, "type": "QValue3DAxis*", "user": false, "write": "setAxisY"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "axisZ", "notify": "axisZChanged", "read": "axisZ", "required": false, "scriptable": true, "stored": true, "type": "QValue3DAxis*", "user": false, "write": "setAxisZ"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "selectedSeries", "notify": "selectedSeriesChanged", "read": "selectedSeries", "required": false, "scriptable": true, "stored": true, "type": "QScatter3DSeries*", "user": false}, {"constant": true, "designable": true, "final": false, "index": 4, "name": "seriesList", "read": "seriesList", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<QScatter3DSeries>", "user": false}], "qualifiedClassName": "QQuickGraphsScatter", "signals": [{"access": "public", "arguments": [{"name": "axis", "type": "QValue3DAxis*"}], "name": "axisXChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QValue3DAxis*"}], "name": "axisYChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QValue3DAxis*"}], "name": "axisZChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "series", "type": "QScatter3DSeries*"}], "name": "selectedSeriesChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "axis", "type": "QAbstract3DAxis*"}], "name": "handleAxisXChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstract3DAxis*"}], "name": "handleAxisYChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstract3DAxis*"}], "name": "handleAxisZChanged", "returnType": "void"}, {"access": "public", "name": "handleSeriesMeshChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enable", "type": "bool"}], "name": "handleMeshSmoothChanged", "returnType": "void"}, {"access": "private", "name": "cameraRotationChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickGraphsItem"}]}], "inputFile": "qquickgraphsscatter_p.h", "outputRevision": 68}, {"classes": [{"classInfos": [{"name": "DefaultProperty", "value": "seriesList"}, {"name": "QML.Element", "value": "Surface3D"}], "className": "QQuickGraphsSurface", "methods": [{"access": "public", "arguments": [{"name": "series", "type": "QSurface3DSeries*"}], "name": "addSeries", "returnType": "void"}, {"access": "public", "arguments": [{"name": "series", "type": "QSurface3DSeries*"}], "name": "removeSeries", "returnType": "void"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "axisX", "notify": "axisXChanged", "read": "axisX", "required": false, "scriptable": true, "stored": true, "type": "QValue3DAxis*", "user": false, "write": "setAxisX"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "axisY", "notify": "axisYChanged", "read": "axisY", "required": false, "scriptable": true, "stored": true, "type": "QValue3DAxis*", "user": false, "write": "setAxisY"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "axisZ", "notify": "axisZChanged", "read": "axisZ", "required": false, "scriptable": true, "stored": true, "type": "QValue3DAxis*", "user": false, "write": "setAxisZ"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "selectedSeries", "notify": "selectedSeriesChanged", "read": "selectedSeries", "required": false, "scriptable": true, "stored": true, "type": "QSurface3DSeries*", "user": false}, {"constant": true, "designable": true, "final": false, "index": 4, "name": "seriesList", "read": "seriesList", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<QSurface3DSeries>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "flipHorizontalGrid", "notify": "flipHorizontalGridChanged", "read": "flipHorizontalGrid", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setFlipHorizontalGrid"}], "qualifiedClassName": "QQuickGraphsSurface", "signals": [{"access": "public", "arguments": [{"name": "axis", "type": "QValue3DAxis*"}], "name": "axisXChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QValue3DAxis*"}], "name": "axisYChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QValue3DAxis*"}], "name": "axisZChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "series", "type": "QSurface3DSeries*"}], "name": "selectedSeriesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "flip", "type": "bool"}], "name": "flipHorizontalGridChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "axis", "type": "QAbstract3DAxis*"}], "name": "handleAxisXChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstract3DAxis*"}], "name": "handleAxisYChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstract3DAxis*"}], "name": "handleAxisZChanged", "returnType": "void"}, {"access": "public", "name": "handleFlatShadingEnabledChanged", "returnType": "void"}, {"access": "public", "name": "handleWireframeColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "flip", "type": "bool"}], "name": "handleFlipHorizontalGridChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickGraphsItem"}]}], "inputFile": "qquickgraphssurface_p.h", "outputRevision": 68}, {"classes": [{"className": "QuickGraphsTextureData", "object": true, "qualifiedClassName": "QuickGraphsTextureData", "superClasses": [{"access": "public", "name": "QQuick3DTextureData"}]}], "inputFile": "quickgraphstexturedata_p.h", "outputRevision": 68}, {"classes": [{"className": "Q3DTheme", "enums": [{"isClass": false, "isFlag": false, "name": "ColorStyle", "values": ["ColorStyleUniform", "ColorStyleObjectGradient", "ColorStyleRangeGradient"]}, {"isClass": false, "isFlag": false, "name": "Theme", "values": ["ThemeQt", "ThemePrimaryColors", "ThemeDigia", "ThemeStoneMoss", "ThemeArmyBlue", "ThemeRetro", "ThemeEbony", "ThemeIsabelle", "ThemeUserDefined"]}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "colorStyle", "notify": "colorStyleChanged", "read": "colorStyle", "required": false, "scriptable": true, "stored": true, "type": "Q3DTheme::ColorStyle", "user": false, "write": "setColorStyle"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "backgroundColor", "notify": "backgroundColorChanged", "read": "backgroundColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setBackgroundColor"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "gridLineColor", "notify": "gridLineColorChanged", "read": "gridLineColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setGridLineColor"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "labelBackgroundColor", "notify": "labelBackgroundColorChanged", "read": "labelBackgroundColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setLabelBackgroundColor"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "labelTextColor", "notify": "labelTextColorChanged", "read": "labelTextColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setLabelTextColor"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "lightColor", "notify": "lightColorChanged", "read": "lightColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setLightColor"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "multiHighlightColor", "notify": "multiHighlightColorChanged", "read": "multiHighlightColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setMultiHighlightColor"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "singleHighlightColor", "notify": "singleHighlightColorChanged", "read": "singleHighlightColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setSingleHighlightColor"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "windowColor", "notify": "windowColorChanged", "read": "windowColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setWindowColor"}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "font", "notify": "fontChanged", "read": "font", "required": false, "scriptable": true, "stored": true, "type": "QFont", "user": false, "write": "setFont"}, {"constant": false, "designable": true, "final": false, "index": 10, "name": "multiHighlightGradient", "notify": "multiHighlightGradientChanged", "read": "multiHighlightGradient", "required": false, "scriptable": true, "stored": true, "type": "QLinearGradient", "user": false, "write": "setMultiHighlightGradient"}, {"constant": false, "designable": true, "final": false, "index": 11, "name": "singleHighlightGradient", "notify": "singleHighlightGradientChanged", "read": "singleHighlightGradient", "required": false, "scriptable": true, "stored": true, "type": "QLinearGradient", "user": false, "write": "setSingleHighlightGradient"}, {"constant": false, "designable": true, "final": false, "index": 12, "name": "baseColors", "notify": "baseColorsChanged", "read": "baseColors", "required": false, "scriptable": true, "stored": true, "type": "QList<QColor>", "user": false, "write": "setBaseColors"}, {"constant": false, "designable": true, "final": false, "index": 13, "name": "baseGradients", "notify": "baseGradientsChanged", "read": "baseGradients", "required": false, "scriptable": true, "stored": true, "type": "QList<QLinearGradient>", "user": false, "write": "setBaseGradients"}, {"constant": false, "designable": true, "final": false, "index": 14, "name": "type", "notify": "typeChanged", "read": "type", "required": false, "scriptable": true, "stored": true, "type": "Q3DTheme::Theme", "user": false, "write": "setType"}, {"constant": false, "designable": true, "final": false, "index": 15, "name": "backgroundEnabled", "notify": "backgroundEnabledChanged", "read": "isBackgroundEnabled", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setBackgroundEnabled"}, {"constant": false, "designable": true, "final": false, "index": 16, "name": "gridEnabled", "notify": "gridEnabledChanged", "read": "isGridEnabled", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setGridEnabled"}, {"constant": false, "designable": true, "final": false, "index": 17, "name": "labelBackgroundEnabled", "notify": "labelBackgroundEnabledChanged", "read": "isLabelBackgroundEnabled", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setLabelBackgroundEnabled"}, {"constant": false, "designable": true, "final": false, "index": 18, "name": "labelBorderEnabled", "notify": "labelBorderEnabledChanged", "read": "isLabelBorderEnabled", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setLabelBorderEnabled"}, {"constant": false, "designable": true, "final": false, "index": 19, "name": "labelsEnabled", "notify": "labelsEnabledChanged", "read": "is<PERSON>abelsEnabled", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 20, "name": "ambientLightStrength", "notify": "ambientLightStrengthChanged", "read": "ambientLightStrength", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setAmbientLightStrength"}, {"constant": false, "designable": true, "final": false, "index": 21, "name": "highlightLightStrength", "notify": "highlightLightStrengthChanged", "read": "highlightLightStrength", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setHighlightLightStrength"}, {"constant": false, "designable": true, "final": false, "index": 22, "name": "lightStrength", "notify": "lightStrengthChanged", "read": "lightStrength", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setLightStrength"}, {"constant": false, "designable": true, "final": false, "index": 23, "name": "shadowStrength", "notify": "shadowStrengthChanged", "read": "shadowStrength", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setShadowStrength"}], "qualifiedClassName": "Q3DTheme", "signals": [{"access": "public", "arguments": [{"name": "themeType", "type": "Q3DTheme::Theme"}], "name": "typeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "colors", "type": "QList<QColor>"}], "name": "baseColorsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "name": "backgroundColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "name": "windowColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "name": "labelTextColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "name": "labelBackgroundColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "name": "gridLineColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "name": "singleHighlightColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "name": "multiHighlightColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "name": "lightColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "gradients", "type": "QList<QLinearGradient>"}], "name": "baseGradientsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "gradient", "type": "QLinearGradient"}], "name": "singleHighlightGradientChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "gradient", "type": "QLinearGradient"}], "name": "multiHighlightGradientChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "strength", "type": "float"}], "name": "lightStrengthChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "strength", "type": "float"}], "name": "ambientLightStrengthChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "strength", "type": "float"}], "name": "highlightLightStrengthChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "name": "labelBorderEnabledChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "font", "type": "QFont"}], "name": "fontChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "name": "backgroundEnabledChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "name": "gridEnabledChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "name": "labelBackgroundEnabledChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "style", "type": "Q3DTheme::ColorStyle"}], "name": "colorStyleChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "name": "labelsEnabledChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "strength", "type": "float"}], "name": "shadowStrengthChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "q3dtheme.h", "outputRevision": 68}, {"classes": [{"className": "Q3DThemePrivate", "object": true, "qualifiedClassName": "Q3DThemePrivate", "signals": [{"access": "public", "name": "needRender", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "q3dtheme_p.h", "outputRevision": 68}, {"classes": [{"className": "ThemeManager", "object": true, "qualifiedClassName": "ThemeManager", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "thememanager_p.h", "outputRevision": 68}, {"classes": [{"className": "QAbstract3DAxis", "enums": [{"isClass": false, "isFlag": false, "name": "AxisOrientation", "values": ["AxisOrientationNone", "AxisOrientationX", "AxisOrientationY", "AxisOrientationZ"]}, {"isClass": false, "isFlag": false, "name": "AxisType", "values": ["AxisTypeNone", "AxisTypeCategory", "AxisTypeValue"]}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "title", "notify": "titleChanged", "read": "title", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setTitle"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "labels", "notify": "labelsChanged", "read": "labels", "required": false, "scriptable": true, "stored": true, "type": "QStringList", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "orientation", "notify": "orientationChanged", "read": "orientation", "required": false, "scriptable": true, "stored": true, "type": "QAbstract3DAxis::AxisOrientation", "user": false}, {"constant": true, "designable": true, "final": false, "index": 3, "name": "type", "read": "type", "required": false, "scriptable": true, "stored": true, "type": "QAbstract3DAxis::AxisType", "user": false}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "min", "notify": "minC<PERSON>ed", "read": "min", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setMin"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "max", "notify": "max<PERSON><PERSON>ed", "read": "max", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setMax"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "autoAdjustRange", "notify": "autoAdjustRangeChanged", "read": "isAutoAdjustRange", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAutoAdjustRange"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "labelAutoRotation", "notify": "labelAutoRotationChanged", "read": "labelAutoRotation", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setLabelAutoRotation"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "titleVisible", "notify": "titleVisibilityChanged", "read": "isTitleVisible", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setTitleVisible"}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "titleFixed", "notify": "titleFixed<PERSON>hanged", "read": "isTitleFixed", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setTitleFixed"}], "qualifiedClassName": "QAbstract3DAxis", "signals": [{"access": "public", "arguments": [{"name": "newTitle", "type": "QString"}], "name": "titleChanged", "returnType": "void"}, {"access": "public", "name": "labelsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "orientation", "type": "QAbstract3DAxis::AxisOrientation"}], "name": "orientationChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "value", "type": "float"}], "name": "minC<PERSON>ed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "value", "type": "float"}], "name": "max<PERSON><PERSON>ed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "min", "type": "float"}, {"name": "max", "type": "float"}], "name": "rangeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "autoAdjust", "type": "bool"}], "name": "autoAdjustRangeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "angle", "type": "float"}], "name": "labelAutoRotationChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "visible", "type": "bool"}], "name": "titleVisibilityChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "fixed", "type": "bool"}], "name": "titleFixed<PERSON>hanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qabstract3daxis.h", "outputRevision": 68}, {"classes": [{"className": "QCategory3DAxis", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "labels", "notify": "labelsChanged", "read": "labels", "required": false, "scriptable": true, "stored": true, "type": "QStringList", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}], "qualifiedClassName": "QCategory3DAxis", "superClasses": [{"access": "public", "name": "QAbstract3DAxis"}]}], "inputFile": "qcategory3daxis.h", "outputRevision": 68}, {"classes": [{"className": "QLogValue3DAxisFormatter", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "base", "notify": "baseChanged", "read": "base", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setBase"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "autoSubGrid", "notify": "autoSubGridChanged", "read": "autoSubGrid", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAutoSubGrid"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "showEdgeLabels", "notify": "showEdgeLabelsChanged", "read": "showEdgeLabels", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setShowEdgeLabels"}], "qualifiedClassName": "QLogValue3DAxisFormatter", "signals": [{"access": "public", "arguments": [{"name": "base", "type": "qreal"}], "name": "baseChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "name": "autoSubGridChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "name": "showEdgeLabelsChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QValue3DAxisFormatter"}]}], "inputFile": "qlogvalue3daxisformatter.h", "outputRevision": 68}, {"classes": [{"className": "QValue3DAxis", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "segmentCount", "notify": "segmentCountChanged", "read": "segmentCount", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setSegmentCount"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "subSegmentCount", "notify": "subSegmentCountChanged", "read": "subSegmentCount", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setSubSegmentCount"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "labelFormat", "notify": "labelFormatChanged", "read": "labelFormat", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setLabelFormat"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "formatter", "notify": "formatterChanged", "read": "formatter", "required": false, "scriptable": true, "stored": true, "type": "QValue3DAxisFormatter*", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "reversed", "notify": "reversedChanged", "read": "reversed", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setReversed"}], "qualifiedClassName": "QValue3DAxis", "signals": [{"access": "public", "arguments": [{"name": "count", "type": "int"}], "name": "segmentCountChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "count", "type": "int"}], "name": "subSegmentCountChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "format", "type": "QString"}], "name": "labelFormatChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "formatter", "type": "QValue3DAxisFormatter*"}], "name": "formatterChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enable", "type": "bool"}], "name": "reversedChanged", "returnType": "void"}, {"access": "public", "name": "formatterDirty", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstract3DAxis"}]}], "inputFile": "qvalue3daxis.h", "outputRevision": 68}, {"classes": [{"className": "QValue3DAxisFormatter", "object": true, "qualifiedClassName": "QValue3DAxisFormatter", "slots": [{"access": "private", "name": "markDirtyNoLabelChange", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qvalue3daxisformatter.h", "outputRevision": 68}, {"classes": [{"className": "AbstractItemModelHandler", "object": true, "qualifiedClassName": "AbstractItemModelHandler", "signals": [{"access": "public", "arguments": [{"name": "itemModel", "type": "const QAbstractItemModel*"}], "name": "itemModelChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "parent", "type": "QModelIndex"}, {"name": "start", "type": "int"}, {"name": "end", "type": "int"}], "name": "handleColumnsInserted", "returnType": "void"}, {"access": "public", "arguments": [{"name": "sourceParent", "type": "QModelIndex"}, {"name": "sourceStart", "type": "int"}, {"name": "sourceEnd", "type": "int"}, {"name": "destinationParent", "type": "QModelIndex"}, {"name": "destinationColumn", "type": "int"}], "name": "handleColumnsMoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "parent", "type": "QModelIndex"}, {"name": "start", "type": "int"}, {"name": "end", "type": "int"}], "name": "handleColumnsRemoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "topLeft", "type": "QModelIndex"}, {"name": "bottomRight", "type": "QModelIndex"}, {"name": "roles", "type": "QList<int>"}], "name": "handleDataChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "topLeft", "type": "QModelIndex"}, {"name": "bottomRight", "type": "QModelIndex"}], "isCloned": true, "name": "handleDataChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "parents", "type": "QList<QPersistentModelIndex>"}, {"name": "hint", "type": "QAbstractItemModel::LayoutChangeHint"}], "name": "handleLayoutChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "parents", "type": "QList<QPersistentModelIndex>"}], "isCloned": true, "name": "handleLayoutChanged", "returnType": "void"}, {"access": "public", "isCloned": true, "name": "handleLayoutChanged", "returnType": "void"}, {"access": "public", "name": "handleModelReset", "returnType": "void"}, {"access": "public", "arguments": [{"name": "parent", "type": "QModelIndex"}, {"name": "start", "type": "int"}, {"name": "end", "type": "int"}], "name": "handleRowsInserted", "returnType": "void"}, {"access": "public", "arguments": [{"name": "sourceParent", "type": "QModelIndex"}, {"name": "sourceStart", "type": "int"}, {"name": "sourceEnd", "type": "int"}, {"name": "destinationParent", "type": "QModelIndex"}, {"name": "destinationRow", "type": "int"}], "name": "handleRowsMoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "parent", "type": "QModelIndex"}, {"name": "start", "type": "int"}, {"name": "end", "type": "int"}], "name": "handleRowsRemoved", "returnType": "void"}, {"access": "public", "name": "handleMappingChanged", "returnType": "void"}, {"access": "public", "name": "handlePendingResolve", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "abstractitemmodelhandler_p.h", "outputRevision": 68}, {"classes": [{"className": "BarItemModelHandler", "object": true, "qualifiedClassName": "BarItemModelHandler", "slots": [{"access": "public", "arguments": [{"name": "topLeft", "type": "QModelIndex"}, {"name": "bottomRight", "type": "QModelIndex"}, {"name": "roles", "type": "QList<int>"}], "name": "handleDataChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "topLeft", "type": "QModelIndex"}, {"name": "bottomRight", "type": "QModelIndex"}], "isCloned": true, "name": "handleDataChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "AbstractItemModelHandler"}]}], "inputFile": "baritemmodelhandler_p.h", "outputRevision": 68}, {"classes": [{"className": "QAbstract3DSeries", "enums": [{"isClass": false, "isFlag": false, "name": "SeriesType", "values": ["SeriesTypeNone", "SeriesTypeBar", "SeriesTypeScatter", "SeriesTypeSurface"]}, {"isClass": false, "isFlag": false, "name": "<PERSON><PERSON>", "values": ["MeshUserDefined", "MeshBar", "MeshCube", "MeshPyramid", "MeshCone", "Mesh<PERSON><PERSON><PERSON>", "MeshBevelBar", "MeshBevelCube", "MeshSphere", "MeshMinimal", "MeshArrow", "MeshPoint"]}], "methods": [{"access": "public", "arguments": [{"name": "axis", "type": "QVector3D"}, {"name": "angle", "type": "float"}], "name": "setMeshAxisAndAngle", "returnType": "void"}], "object": true, "properties": [{"constant": true, "designable": true, "final": false, "index": 0, "name": "type", "read": "type", "required": false, "scriptable": true, "stored": true, "type": "QAbstract3DSeries::SeriesType", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "itemLabelFormat", "notify": "itemLabelFormatChanged", "read": "itemLabelFormat", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setItemLabelFormat"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "visible", "notify": "visibilityChanged", "read": "isVisible", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setVisible"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "mesh", "notify": "meshChanged", "read": "mesh", "required": false, "scriptable": true, "stored": true, "type": "QAbstract3DSeries::Mesh", "user": false, "write": "<PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "meshSmooth", "notify": "meshSmoothChanged", "read": "isMeshSmooth", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setMeshSmooth"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "meshRotation", "notify": "meshRotationChanged", "read": "meshRotation", "required": false, "scriptable": true, "stored": true, "type": "QQuaternion", "user": false, "write": "setMeshRotation"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "userDefinedMesh", "notify": "userDefinedMeshChanged", "read": "userDefinedMesh", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setUserDefinedMesh"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "colorStyle", "notify": "colorStyleChanged", "read": "colorStyle", "required": false, "scriptable": true, "stored": true, "type": "Q3DTheme::ColorStyle", "user": false, "write": "setColorStyle"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "baseColor", "notify": "baseColorChanged", "read": "baseColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setBaseColor"}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "baseGradient", "notify": "baseGradientChanged", "read": "baseGradient", "required": false, "scriptable": true, "stored": true, "type": "QLinearGradient", "user": false, "write": "setBaseGradient"}, {"constant": false, "designable": true, "final": false, "index": 10, "name": "singleHighlightColor", "notify": "singleHighlightColorChanged", "read": "singleHighlightColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setSingleHighlightColor"}, {"constant": false, "designable": true, "final": false, "index": 11, "name": "singleHighlightGradient", "notify": "singleHighlightGradientChanged", "read": "singleHighlightGradient", "required": false, "scriptable": true, "stored": true, "type": "QLinearGradient", "user": false, "write": "setSingleHighlightGradient"}, {"constant": false, "designable": true, "final": false, "index": 12, "name": "multiHighlightColor", "notify": "multiHighlightColorChanged", "read": "multiHighlightColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setMultiHighlightColor"}, {"constant": false, "designable": true, "final": false, "index": 13, "name": "multiHighlightGradient", "notify": "multiHighlightGradientChanged", "read": "multiHighlightGradient", "required": false, "scriptable": true, "stored": true, "type": "QLinearGradient", "user": false, "write": "setMultiHighlightGradient"}, {"constant": false, "designable": true, "final": false, "index": 14, "name": "name", "notify": "nameChanged", "read": "name", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setName"}, {"constant": false, "designable": true, "final": false, "index": 15, "name": "itemLabel", "notify": "itemLabelChanged", "read": "itemLabel", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": false, "designable": true, "final": false, "index": 16, "name": "itemLabelVisible", "notify": "itemLabelVisibilityChanged", "read": "isItemLabelVisible", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setItemLabelVisible"}], "qualifiedClassName": "QAbstract3DSeries", "signals": [{"access": "public", "arguments": [{"name": "format", "type": "QString"}], "name": "itemLabelFormatChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "visible", "type": "bool"}], "name": "visibilityChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "mesh", "type": "QAbstract3DSeries::Mesh"}], "name": "meshChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "name": "meshSmoothChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "rotation", "type": "QQuaternion"}], "name": "meshRotationChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "fileName", "type": "QString"}], "name": "userDefinedMeshChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "style", "type": "Q3DTheme::ColorStyle"}], "name": "colorStyleChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "name": "baseColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "gradient", "type": "QLinearGradient"}], "name": "baseGradientChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "name": "singleHighlightColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "gradient", "type": "QLinearGradient"}], "name": "singleHighlightGradientChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "name": "multiHighlightColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "gradient", "type": "QLinearGradient"}], "name": "multiHighlightGradientChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "name", "type": "QString"}], "name": "nameChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "label", "type": "QString"}], "name": "itemLabelChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "visible", "type": "bool"}], "name": "itemLabelVisibilityChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qabstract3dseries.h", "outputRevision": 68}, {"classes": [{"className": "QAbstractDataProxy", "enums": [{"isClass": false, "isFlag": false, "name": "DataType", "values": ["DataTypeNone", "DataTypeBar", "DataTypeScatter", "DataTypeSurface"]}], "object": true, "properties": [{"constant": true, "designable": true, "final": false, "index": 0, "name": "type", "read": "type", "required": false, "scriptable": true, "stored": true, "type": "QAbstractDataProxy::DataType", "user": false}], "qualifiedClassName": "QAbstractDataProxy", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qabstractdataproxy.h", "outputRevision": 68}, {"classes": [{"className": "QBar3DSeries", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "dataProxy", "notify": "dataProxyChanged", "read": "dataProxy", "required": false, "scriptable": true, "stored": true, "type": "QBarDataProxy*", "user": false, "write": "setDataProxy"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "<PERSON><PERSON><PERSON>", "notify": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "read": "<PERSON><PERSON><PERSON>", "required": false, "scriptable": true, "stored": true, "type": "QPoint", "user": false, "write": "setSelectedBar"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "meshAngle", "notify": "meshAngleChanged", "read": "meshAngle", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setMeshAngle"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "rowColors", "notify": "rowColorsChanged", "read": "rowColors", "required": false, "scriptable": true, "stored": true, "type": "QList<QColor>", "user": false, "write": "setRowColors"}], "qualifiedClassName": "QBar3DSeries", "signals": [{"access": "public", "arguments": [{"name": "proxy", "type": "QBarDataProxy*"}], "name": "dataProxyChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "position", "type": "QPoint"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "arguments": [{"name": "angle", "type": "float"}], "name": "meshAngleChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "rowcolors", "type": "QList<QColor>"}], "name": "rowColorsChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstract3DSeries"}]}], "inputFile": "qbar3dseries.h", "outputRevision": 68}, {"classes": [{"className": "QBarDataProxy", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "rowCount", "notify": "rowCountChanged", "read": "rowCount", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "col<PERSON>ount", "notify": "colCountChanged", "read": "col<PERSON>ount", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "<PERSON><PERSON><PERSON><PERSON>", "notify": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "read": "<PERSON><PERSON><PERSON><PERSON>", "required": false, "scriptable": true, "stored": true, "type": "QStringList", "user": false, "write": "set<PERSON>ow<PERSON>abe<PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "columnLabels", "notify": "columnLabelsChanged", "read": "columnLabels", "required": false, "scriptable": true, "stored": true, "type": "QStringList", "user": false, "write": "setColumnLabels"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "series", "notify": "seriesChanged", "read": "series", "required": false, "scriptable": true, "stored": true, "type": "QBar3DSeries*", "user": false}], "qualifiedClassName": "QBarDataProxy", "signals": [{"access": "public", "name": "arrayReset", "returnType": "void"}, {"access": "public", "arguments": [{"name": "startIndex", "type": "int"}, {"name": "count", "type": "int"}], "name": "rowsAdded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "startIndex", "type": "int"}, {"name": "count", "type": "int"}], "name": "rowsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "startIndex", "type": "int"}, {"name": "count", "type": "int"}], "name": "rowsRemoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "startIndex", "type": "int"}, {"name": "count", "type": "int"}], "name": "rowsInserted", "returnType": "void"}, {"access": "public", "arguments": [{"name": "rowIndex", "type": "int"}, {"name": "columnIndex", "type": "int"}], "name": "itemChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "count", "type": "int"}], "name": "rowCountChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "count", "type": "int"}], "name": "colCountChanged", "returnType": "void"}, {"access": "public", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "name": "columnLabelsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "series", "type": "QBar3DSeries*"}], "name": "seriesChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractDataProxy"}]}], "inputFile": "qbardataproxy.h", "outputRevision": 68}, {"classes": [{"className": "QCustom3DItem", "methods": [{"access": "public", "arguments": [{"name": "axis", "type": "QVector3D"}, {"name": "angle", "type": "float"}], "name": "setRotationAxisAndAngle", "returnType": "void"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "meshFile", "notify": "meshFileChanged", "read": "meshFile", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setMeshFile"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "textureFile", "notify": "textureFileChanged", "read": "textureFile", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setTextureFile"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "position", "notify": "positionChanged", "read": "position", "required": false, "scriptable": true, "stored": true, "type": "QVector3D", "user": false, "write": "setPosition"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "positionAbsolute", "notify": "positionAbsoluteChanged", "read": "isPositionAbsolute", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setPositionAbsolute"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "scaling", "notify": "scalingChanged", "read": "scaling", "required": false, "scriptable": true, "stored": true, "type": "QVector3D", "user": false, "write": "setScaling"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "rotation", "notify": "rotationChanged", "read": "rotation", "required": false, "scriptable": true, "stored": true, "type": "QQuaternion", "user": false, "write": "setRotation"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "visible", "notify": "visibleChanged", "read": "isVisible", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setVisible"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "shadowCasting", "notify": "shadowCastingChanged", "read": "isShadowCasting", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setShadowCasting"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "scalingAbsolute", "notify": "scalingAbsoluteChanged", "read": "isScalingAbsolute", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setScalingAbsolute"}], "qualifiedClassName": "QCustom3DItem", "signals": [{"access": "public", "arguments": [{"name": "meshFile", "type": "QString"}], "name": "meshFileChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "textureFile", "type": "QString"}], "name": "textureFileChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "position", "type": "QVector3D"}], "name": "positionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "positionAbsolute", "type": "bool"}], "name": "positionAbsoluteChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "scaling", "type": "QVector3D"}], "name": "scalingChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "rotation", "type": "QQuaternion"}], "name": "rotationChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "visible", "type": "bool"}], "name": "visibleChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "shadowCasting", "type": "bool"}], "name": "shadowCastingChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "scalingAbsolute", "type": "bool"}], "name": "scalingAbsoluteChanged", "returnType": "void"}, {"access": "public", "name": "needUpdate", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qcustom3ditem.h", "outputRevision": 68}, {"classes": [{"className": "QCustom3DLabel", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "text", "notify": "textChanged", "read": "text", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setText"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "font", "notify": "fontChanged", "read": "font", "required": false, "scriptable": true, "stored": true, "type": "QFont", "user": false, "write": "setFont"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "textColor", "notify": "textColorChanged", "read": "textColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setTextColor"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "backgroundColor", "notify": "backgroundColorChanged", "read": "backgroundColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setBackgroundColor"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "borderEnabled", "notify": "borderEnabledChanged", "read": "isBorderEnabled", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setBorderEnabled"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "backgroundEnabled", "notify": "backgroundEnabledChanged", "read": "isBackgroundEnabled", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setBackgroundEnabled"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "facingCamera", "notify": "facingCameraChanged", "read": "isFacingCamera", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setFacingCamera"}], "qualifiedClassName": "QCustom3DLabel", "signals": [{"access": "public", "arguments": [{"name": "text", "type": "QString"}], "name": "textChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "font", "type": "QFont"}], "name": "fontChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "name": "textColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "name": "backgroundColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "name": "borderEnabledChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "name": "backgroundEnabledChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "name": "facingCameraChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QCustom3DItem"}]}], "inputFile": "qcustom3dlabel.h", "outputRevision": 68}, {"classes": [{"className": "QCustom3DVolume", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "textureWidth", "notify": "textureWidthChanged", "read": "textureWidth", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setTextureWidth"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "textureHeight", "notify": "textureHeightChanged", "read": "textureHeight", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setTextureHeight"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "textureDepth", "notify": "textureDepthChanged", "read": "textureDepth", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setTextureDepth"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "sliceIndexX", "notify": "sliceIndexXChanged", "read": "sliceIndexX", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setSliceIndexX"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "sliceIndexY", "notify": "sliceIndexYChanged", "read": "sliceIndexY", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setSliceIndexY"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "sliceIndexZ", "notify": "sliceIndexZChanged", "read": "sliceIndexZ", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setSliceIndexZ"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "colorTable", "notify": "colorTableChanged", "read": "colorTable", "required": false, "scriptable": true, "stored": true, "type": "QList<QRgb>", "user": false, "write": "setColorTable"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "textureData", "notify": "textureDataChanged", "read": "textureData", "required": false, "scriptable": true, "stored": true, "type": "QList<uchar>*", "user": false, "write": "setTextureData"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "alphaMultiplier", "notify": "alphaMultiplierChanged", "read": "alphaMultiplier", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setAlphaMultiplier"}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "preserveOpacity", "notify": "preserveOpacityChanged", "read": "preserveOpacity", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setPreserveOpacity"}, {"constant": false, "designable": true, "final": false, "index": 10, "name": "useHighDefShader", "notify": "useHighDefShaderChanged", "read": "useHighDefShader", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setUseHighDefShader"}, {"constant": false, "designable": true, "final": false, "index": 11, "name": "drawSlices", "notify": "drawSlicesChanged", "read": "drawSlices", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setDrawSlices"}, {"constant": false, "designable": true, "final": false, "index": 12, "name": "drawSliceFrames", "notify": "drawSliceFramesChanged", "read": "drawSliceFrames", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setDrawSliceFrames"}, {"constant": false, "designable": true, "final": false, "index": 13, "name": "sliceFrameColor", "notify": "sliceFrameColorChanged", "read": "sliceFrameColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setSliceFrameColor"}, {"constant": false, "designable": true, "final": false, "index": 14, "name": "sliceFrameWidths", "notify": "sliceFrameWidthsChanged", "read": "sliceFrameWidths", "required": false, "scriptable": true, "stored": true, "type": "QVector3D", "user": false, "write": "setSliceFrameWidths"}, {"constant": false, "designable": true, "final": false, "index": 15, "name": "sliceFrameGaps", "notify": "sliceFrameGapsChanged", "read": "sliceFrameGaps", "required": false, "scriptable": true, "stored": true, "type": "QVector3D", "user": false, "write": "setSliceFrameGaps"}, {"constant": false, "designable": true, "final": false, "index": 16, "name": "sliceFrameThicknesses", "notify": "sliceFrameThicknessesChanged", "read": "sliceFrameThicknesses", "required": false, "scriptable": true, "stored": true, "type": "QVector3D", "user": false, "write": "setSliceFrameThicknesses"}], "qualifiedClassName": "QCustom3DVolume", "signals": [{"access": "public", "arguments": [{"name": "value", "type": "int"}], "name": "textureWidthChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "value", "type": "int"}], "name": "textureHeightChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "value", "type": "int"}], "name": "textureDepthChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "value", "type": "int"}], "name": "sliceIndexXChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "value", "type": "int"}], "name": "sliceIndexYChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "value", "type": "int"}], "name": "sliceIndexZChanged", "returnType": "void"}, {"access": "public", "name": "colorTableChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "data", "type": "QList<uchar>*"}], "name": "textureDataChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "format", "type": "QImage::Format"}], "name": "textureFormatChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "mult", "type": "float"}], "name": "alphaMultiplierChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "name": "preserveOpacityChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "name": "useHighDefShaderChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "name": "drawSlicesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "name": "drawSliceFramesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "name": "sliceFrameColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "values", "type": "QVector3D"}], "name": "sliceFrameWidthsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "values", "type": "QVector3D"}], "name": "sliceFrameGapsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "values", "type": "QVector3D"}], "name": "sliceFrameThicknessesChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QCustom3DItem"}]}], "inputFile": "qcustom3dvolume.h", "outputRevision": 68}, {"classes": [{"className": "QHeightMapSurfaceDataProxy", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "heightMap", "notify": "heightMapChanged", "read": "heightMap", "required": false, "scriptable": true, "stored": true, "type": "QImage", "user": false, "write": "setHeightMap"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "heightMapFile", "notify": "heightMapFileChanged", "read": "heightMapFile", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setHeightMapFile"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "minXValue", "notify": "minXValueChanged", "read": "minXValue", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setMinXValue"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "maxXValue", "notify": "maxXValueChanged", "read": "maxXValue", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setMaxXValue"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "minZValue", "notify": "minZValueChanged", "read": "minZValue", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setMinZValue"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "maxZValue", "notify": "maxZ<PERSON><PERSON>ue<PERSON>hanged", "read": "maxZValue", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setMaxZValue"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "minYValue", "notify": "minYValueChanged", "read": "minYValue", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setMinYValue"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "maxYValue", "notify": "maxYValueChanged", "read": "maxYValue", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setMaxYValue"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "autoScaleY", "notify": "autoScaleYChanged", "read": "autoScaleY", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAutoScaleY"}], "qualifiedClassName": "QHeightMapSurfaceDataProxy", "signals": [{"access": "public", "arguments": [{"name": "image", "type": "QImage"}], "name": "heightMapChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "filename", "type": "QString"}], "name": "heightMapFileChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "value", "type": "float"}], "name": "minXValueChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "value", "type": "float"}], "name": "maxXValueChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "value", "type": "float"}], "name": "minZValueChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "value", "type": "float"}], "name": "maxZ<PERSON><PERSON>ue<PERSON>hanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "value", "type": "float"}], "name": "minYValueChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "value", "type": "float"}], "name": "maxYValueChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "name": "autoScaleYChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QSurfaceDataProxy"}]}], "inputFile": "qheightmapsurfacedataproxy.h", "outputRevision": 68}, {"classes": [{"className": "QItemModelBarDataProxy", "enums": [{"isClass": false, "isFlag": false, "name": "MultiMatchBehavior", "values": ["MMBFirst", "MMBLast", "MMBAverage", "MMBCumulative"]}], "methods": [{"access": "public", "arguments": [{"name": "category", "type": "QString"}], "name": "rowCategoryIndex", "returnType": "int"}, {"access": "public", "arguments": [{"name": "category", "type": "QString"}], "name": "columnCategoryIndex", "returnType": "int"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "itemModel", "notify": "itemModelChanged", "read": "itemModel", "required": false, "scriptable": true, "stored": true, "type": "QAbstractItemModel*", "user": false, "write": "setItemModel"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "rowRole", "notify": "rowRoleChanged", "read": "rowRole", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setRowRole"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "columnRole", "notify": "columnRoleChanged", "read": "columnRole", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setColumnRole"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "valueRole", "notify": "valueRoleChanged", "read": "valueRole", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setValueRole"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "rotationRole", "notify": "rotationRoleChanged", "read": "rotationRole", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setRotationRole"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "rowCategories", "notify": "rowCategoriesChanged", "read": "rowCategories", "required": false, "scriptable": true, "stored": true, "type": "QStringList", "user": false, "write": "setRowCategories"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "columnCategories", "notify": "columnCategoriesChanged", "read": "columnCategories", "required": false, "scriptable": true, "stored": true, "type": "QStringList", "user": false, "write": "setColumnCategories"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "useModelCategories", "notify": "useModelCategoriesChanged", "read": "useModelCategories", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setUseModelCategories"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "autoRowCategories", "notify": "autoRowCategoriesChanged", "read": "autoRowCategories", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAutoRowCategories"}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "autoColumnCategories", "notify": "autoColumnCategoriesChanged", "read": "autoColumnCategories", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAutoColumnCategories"}, {"constant": false, "designable": true, "final": false, "index": 10, "name": "rowRolePattern", "notify": "rowRolePatternChanged", "read": "rowRolePattern", "required": false, "scriptable": true, "stored": true, "type": "QRegularExpression", "user": false, "write": "setRowRolePattern"}, {"constant": false, "designable": true, "final": false, "index": 11, "name": "columnRolePattern", "notify": "columnRolePatternChanged", "read": "columnRolePattern", "required": false, "scriptable": true, "stored": true, "type": "QRegularExpression", "user": false, "write": "setColumnRolePattern"}, {"constant": false, "designable": true, "final": false, "index": 12, "name": "valueRolePattern", "notify": "valueRolePatternChanged", "read": "valueRolePattern", "required": false, "scriptable": true, "stored": true, "type": "QRegularExpression", "user": false, "write": "setValueRolePattern"}, {"constant": false, "designable": true, "final": false, "index": 13, "name": "rotationRolePattern", "notify": "rotationRolePatternChanged", "read": "rotationRolePattern", "required": false, "scriptable": true, "stored": true, "type": "QRegularExpression", "user": false, "write": "setRotationRolePattern"}, {"constant": false, "designable": true, "final": false, "index": 14, "name": "row<PERSON><PERSON><PERSON><PERSON>lace", "notify": "rowRoleReplaceChanged", "read": "row<PERSON><PERSON><PERSON><PERSON>lace", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setRowRoleReplace"}, {"constant": false, "designable": true, "final": false, "index": 15, "name": "columnRole<PERSON>eplace", "notify": "columnRoleReplaceChanged", "read": "columnRole<PERSON>eplace", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setColumnRoleReplace"}, {"constant": false, "designable": true, "final": false, "index": 16, "name": "valueRoleReplace", "notify": "valueRoleReplaceChanged", "read": "valueRoleReplace", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "set<PERSON><PERSON><PERSON><PERSON>oleReplace"}, {"constant": false, "designable": true, "final": false, "index": 17, "name": "rotation<PERSON><PERSON><PERSON><PERSON><PERSON>", "notify": "rotationRoleReplaceChanged", "read": "rotation<PERSON><PERSON><PERSON><PERSON><PERSON>", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setRotationRoleReplace"}, {"constant": false, "designable": true, "final": false, "index": 18, "name": "multiMatchBehavior", "notify": "multiMatchBehaviorChanged", "read": "multiMatchBehavior", "required": false, "scriptable": true, "stored": true, "type": "QItemModelBarDataProxy::MultiMatchBehavior", "user": false, "write": "setMultiMatchBehavior"}], "qualifiedClassName": "QItemModelBarDataProxy", "signals": [{"access": "public", "arguments": [{"name": "itemModel", "type": "const QAbstractItemModel*"}], "name": "itemModelChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "role", "type": "QString"}], "name": "rowRoleChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "role", "type": "QString"}], "name": "columnRoleChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "role", "type": "QString"}], "name": "valueRoleChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "role", "type": "QString"}], "name": "rotationRoleChanged", "returnType": "void"}, {"access": "public", "name": "rowCategoriesChanged", "returnType": "void"}, {"access": "public", "name": "columnCategoriesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enable", "type": "bool"}], "name": "useModelCategoriesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enable", "type": "bool"}], "name": "autoRowCategoriesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enable", "type": "bool"}], "name": "autoColumnCategoriesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "pattern", "type": "QRegularExpression"}], "name": "rowRolePatternChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "pattern", "type": "QRegularExpression"}], "name": "columnRolePatternChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "pattern", "type": "QRegularExpression"}], "name": "valueRolePatternChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "pattern", "type": "QRegularExpression"}], "name": "rotationRolePatternChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "replace", "type": "QString"}], "name": "rowRoleReplaceChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "replace", "type": "QString"}], "name": "columnRoleReplaceChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "replace", "type": "QString"}], "name": "valueRoleReplaceChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "replace", "type": "QString"}], "name": "rotationRoleReplaceChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "behavior", "type": "QItemModelBarDataProxy::MultiMatchBehavior"}], "name": "multiMatchBehaviorChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QBarDataProxy"}]}], "inputFile": "qitemmodelbardataproxy.h", "outputRevision": 68}, {"classes": [{"className": "QItemModelScatterDataProxy", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "itemModel", "notify": "itemModelChanged", "read": "itemModel", "required": false, "scriptable": true, "stored": true, "type": "QAbstractItemModel*", "user": false, "write": "setItemModel"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "xPosRole", "notify": "xPosRoleChanged", "read": "xPosRole", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setXPosRole"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "yPosRole", "notify": "yPosRoleChanged", "read": "yPosRole", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setYPosRole"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "zPosRole", "notify": "zPosRoleChanged", "read": "zPosRole", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setZPosRole"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "rotationRole", "notify": "rotationRoleChanged", "read": "rotationRole", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setRotationRole"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "xPosRolePattern", "notify": "xPosRolePatternChanged", "read": "xPosRolePattern", "required": false, "scriptable": true, "stored": true, "type": "QRegularExpression", "user": false, "write": "setXPosRolePattern"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "yPosRolePattern", "notify": "yPosRolePatternChanged", "read": "yPosRolePattern", "required": false, "scriptable": true, "stored": true, "type": "QRegularExpression", "user": false, "write": "setYPosRolePattern"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "zPosRolePattern", "notify": "zPosRolePatternChanged", "read": "zPosRolePattern", "required": false, "scriptable": true, "stored": true, "type": "QRegularExpression", "user": false, "write": "setZPosRolePattern"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "rotationRolePattern", "notify": "rotationRolePatternChanged", "read": "rotationRolePattern", "required": false, "scriptable": true, "stored": true, "type": "QRegularExpression", "user": false, "write": "setRotationRolePattern"}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "xPosRoleReplace", "notify": "xPosRoleReplaceChanged", "read": "xPosRoleReplace", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setXPosRoleReplace"}, {"constant": false, "designable": true, "final": false, "index": 10, "name": "yPosRoleReplace", "notify": "yPosRoleReplaceChanged", "read": "yPosRoleReplace", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setYPosRoleReplace"}, {"constant": false, "designable": true, "final": false, "index": 11, "name": "zPosRoleReplace", "notify": "zPosRoleReplaceChanged", "read": "zPosRoleReplace", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setZPosRoleReplace"}, {"constant": false, "designable": true, "final": false, "index": 12, "name": "rotation<PERSON><PERSON><PERSON><PERSON><PERSON>", "notify": "rotationRoleReplaceChanged", "read": "rotation<PERSON><PERSON><PERSON><PERSON><PERSON>", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setRotationRoleReplace"}], "qualifiedClassName": "QItemModelScatterDataProxy", "signals": [{"access": "public", "arguments": [{"name": "itemModel", "type": "const QAbstractItemModel*"}], "name": "itemModelChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "role", "type": "QString"}], "name": "xPosRoleChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "role", "type": "QString"}], "name": "yPosRoleChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "role", "type": "QString"}], "name": "zPosRoleChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "role", "type": "QString"}], "name": "rotationRoleChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "pattern", "type": "QRegularExpression"}], "name": "xPosRolePatternChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "pattern", "type": "QRegularExpression"}], "name": "yPosRolePatternChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "pattern", "type": "QRegularExpression"}], "name": "zPosRolePatternChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "pattern", "type": "QRegularExpression"}], "name": "rotationRolePatternChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "replace", "type": "QString"}], "name": "rotationRoleReplaceChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "replace", "type": "QString"}], "name": "xPosRoleReplaceChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "replace", "type": "QString"}], "name": "yPosRoleReplaceChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "replace", "type": "QString"}], "name": "zPosRoleReplaceChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QScatterDataProxy"}]}], "inputFile": "qitemmodelscatterdataproxy.h", "outputRevision": 68}, {"classes": [{"className": "QItemModelSurfaceDataProxy", "enums": [{"isClass": false, "isFlag": false, "name": "MultiMatchBehavior", "values": ["MMBFirst", "MMBLast", "MMBAverage", "MMBCumulativeY"]}], "methods": [{"access": "public", "arguments": [{"name": "category", "type": "QString"}], "name": "rowCategoryIndex", "returnType": "int"}, {"access": "public", "arguments": [{"name": "category", "type": "QString"}], "name": "columnCategoryIndex", "returnType": "int"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "itemModel", "notify": "itemModelChanged", "read": "itemModel", "required": false, "scriptable": true, "stored": true, "type": "QAbstractItemModel*", "user": false, "write": "setItemModel"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "rowRole", "notify": "rowRoleChanged", "read": "rowRole", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setRowRole"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "columnRole", "notify": "columnRoleChanged", "read": "columnRole", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setColumnRole"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "xPosRole", "notify": "xPosRoleChanged", "read": "xPosRole", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setXPosRole"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "yPosRole", "notify": "yPosRoleChanged", "read": "yPosRole", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setYPosRole"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "zPosRole", "notify": "zPosRoleChanged", "read": "zPosRole", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setZPosRole"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "rowCategories", "notify": "rowCategoriesChanged", "read": "rowCategories", "required": false, "scriptable": true, "stored": true, "type": "QStringList", "user": false, "write": "setRowCategories"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "columnCategories", "notify": "columnCategoriesChanged", "read": "columnCategories", "required": false, "scriptable": true, "stored": true, "type": "QStringList", "user": false, "write": "setColumnCategories"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "useModelCategories", "notify": "useModelCategoriesChanged", "read": "useModelCategories", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setUseModelCategories"}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "autoRowCategories", "notify": "autoRowCategoriesChanged", "read": "autoRowCategories", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAutoRowCategories"}, {"constant": false, "designable": true, "final": false, "index": 10, "name": "autoColumnCategories", "notify": "autoColumnCategoriesChanged", "read": "autoColumnCategories", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAutoColumnCategories"}, {"constant": false, "designable": true, "final": false, "index": 11, "name": "rowRolePattern", "notify": "rowRolePatternChanged", "read": "rowRolePattern", "required": false, "scriptable": true, "stored": true, "type": "QRegularExpression", "user": false, "write": "setRowRolePattern"}, {"constant": false, "designable": true, "final": false, "index": 12, "name": "columnRolePattern", "notify": "columnRolePatternChanged", "read": "columnRolePattern", "required": false, "scriptable": true, "stored": true, "type": "QRegularExpression", "user": false, "write": "setColumnRolePattern"}, {"constant": false, "designable": true, "final": false, "index": 13, "name": "xPosRolePattern", "notify": "xPosRolePatternChanged", "read": "xPosRolePattern", "required": false, "scriptable": true, "stored": true, "type": "QRegularExpression", "user": false, "write": "setXPosRolePattern"}, {"constant": false, "designable": true, "final": false, "index": 14, "name": "yPosRolePattern", "notify": "yPosRolePatternChanged", "read": "yPosRolePattern", "required": false, "scriptable": true, "stored": true, "type": "QRegularExpression", "user": false, "write": "setYPosRolePattern"}, {"constant": false, "designable": true, "final": false, "index": 15, "name": "zPosRolePattern", "notify": "zPosRolePatternChanged", "read": "zPosRolePattern", "required": false, "scriptable": true, "stored": true, "type": "QRegularExpression", "user": false, "write": "setZPosRolePattern"}, {"constant": false, "designable": true, "final": false, "index": 16, "name": "row<PERSON><PERSON><PERSON><PERSON>lace", "notify": "rowRoleReplaceChanged", "read": "row<PERSON><PERSON><PERSON><PERSON>lace", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setRowRoleReplace"}, {"constant": false, "designable": true, "final": false, "index": 17, "name": "columnRole<PERSON>eplace", "notify": "columnRoleReplaceChanged", "read": "columnRole<PERSON>eplace", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setColumnRoleReplace"}, {"constant": false, "designable": true, "final": false, "index": 18, "name": "xPosRoleReplace", "notify": "xPosRoleReplaceChanged", "read": "xPosRoleReplace", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setXPosRoleReplace"}, {"constant": false, "designable": true, "final": false, "index": 19, "name": "yPosRoleReplace", "notify": "yPosRoleReplaceChanged", "read": "yPosRoleReplace", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setYPosRoleReplace"}, {"constant": false, "designable": true, "final": false, "index": 20, "name": "zPosRoleReplace", "notify": "zPosRoleReplaceChanged", "read": "zPosRoleReplace", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setZPosRoleReplace"}, {"constant": false, "designable": true, "final": false, "index": 21, "name": "multiMatchBehavior", "notify": "multiMatchBehaviorChanged", "read": "multiMatchBehavior", "required": false, "scriptable": true, "stored": true, "type": "QItemModelSurfaceDataProxy::MultiMatchBehavior", "user": false, "write": "setMultiMatchBehavior"}], "qualifiedClassName": "QItemModelSurfaceDataProxy", "signals": [{"access": "public", "arguments": [{"name": "itemModel", "type": "const QAbstractItemModel*"}], "name": "itemModelChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "role", "type": "QString"}], "name": "rowRoleChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "role", "type": "QString"}], "name": "columnRoleChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "role", "type": "QString"}], "name": "xPosRoleChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "role", "type": "QString"}], "name": "yPosRoleChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "role", "type": "QString"}], "name": "zPosRoleChanged", "returnType": "void"}, {"access": "public", "name": "rowCategoriesChanged", "returnType": "void"}, {"access": "public", "name": "columnCategoriesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enable", "type": "bool"}], "name": "useModelCategoriesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enable", "type": "bool"}], "name": "autoRowCategoriesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enable", "type": "bool"}], "name": "autoColumnCategoriesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "pattern", "type": "QRegularExpression"}], "name": "rowRolePatternChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "pattern", "type": "QRegularExpression"}], "name": "columnRolePatternChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "pattern", "type": "QRegularExpression"}], "name": "xPosRolePatternChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "pattern", "type": "QRegularExpression"}], "name": "yPosRolePatternChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "pattern", "type": "QRegularExpression"}], "name": "zPosRolePatternChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "replace", "type": "QString"}], "name": "rowRoleReplaceChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "replace", "type": "QString"}], "name": "columnRoleReplaceChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "replace", "type": "QString"}], "name": "xPosRoleReplaceChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "replace", "type": "QString"}], "name": "yPosRoleReplaceChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "replace", "type": "QString"}], "name": "zPosRoleReplaceChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "behavior", "type": "QItemModelSurfaceDataProxy::MultiMatchBehavior"}], "name": "multiMatchBehaviorChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QSurfaceDataProxy"}]}], "inputFile": "qitemmodelsurfacedataproxy.h", "outputRevision": 68}, {"classes": [{"className": "QScatter3DSeries", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "dataProxy", "notify": "dataProxyChanged", "read": "dataProxy", "required": false, "scriptable": true, "stored": true, "type": "QScatterDataProxy*", "user": false, "write": "setDataProxy"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "selectedItem", "notify": "selectedItemChanged", "read": "selectedItem", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setSelectedItem"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "itemSize", "notify": "itemSizeChanged", "read": "itemSize", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setItemSize"}], "qualifiedClassName": "QScatter3DSeries", "signals": [{"access": "public", "arguments": [{"name": "proxy", "type": "QScatterDataProxy*"}], "name": "dataProxyChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}], "name": "selectedItemChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "size", "type": "float"}], "name": "itemSizeChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstract3DSeries"}]}], "inputFile": "qscatter3dseries.h", "outputRevision": 68}, {"classes": [{"className": "QScatterDataProxy", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "itemCount", "notify": "itemCountChanged", "read": "itemCount", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "series", "notify": "seriesChanged", "read": "series", "required": false, "scriptable": true, "stored": true, "type": "QScatter3DSeries*", "user": false}], "qualifiedClassName": "QScatterDataProxy", "signals": [{"access": "public", "name": "arrayReset", "returnType": "void"}, {"access": "public", "arguments": [{"name": "startIndex", "type": "int"}, {"name": "count", "type": "int"}], "name": "itemsAdded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "startIndex", "type": "int"}, {"name": "count", "type": "int"}], "name": "itemsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "startIndex", "type": "int"}, {"name": "count", "type": "int"}], "name": "itemsRemoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "startIndex", "type": "int"}, {"name": "count", "type": "int"}], "name": "itemsInserted", "returnType": "void"}, {"access": "public", "arguments": [{"name": "count", "type": "int"}], "name": "itemCountChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "series", "type": "QScatter3DSeries*"}], "name": "seriesChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractDataProxy"}]}], "inputFile": "qscatterdataproxy.h", "outputRevision": 68}, {"classes": [{"className": "QSurface3DSeries", "enums": [{"isClass": false, "isFlag": false, "name": "DrawFlag", "values": ["DrawWireframe", "DrawSurface", "DrawSurfaceAndWireframe"]}, {"alias": "DrawFlag", "isClass": false, "isFlag": true, "name": "DrawFlags", "values": ["DrawWireframe", "DrawSurface", "DrawSurfaceAndWireframe"]}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "dataProxy", "notify": "dataProxyChanged", "read": "dataProxy", "required": false, "scriptable": true, "stored": true, "type": "QSurfaceDataProxy*", "user": false, "write": "setDataProxy"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "selectedPoint", "notify": "selectedPointChanged", "read": "selectedPoint", "required": false, "scriptable": true, "stored": true, "type": "QPoint", "user": false, "write": "setSelectedPoint"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "flatShadingEnabled", "notify": "flatShadingEnabledChanged", "read": "isFlatShadingEnabled", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setFlatShadingEnabled"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "flatShadingSupported", "notify": "flatShadingSupportedChanged", "read": "isFlatShadingSupported", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "drawMode", "notify": "drawModeChanged", "read": "drawMode", "required": false, "scriptable": true, "stored": true, "type": "QSurface3DSeries::DrawFlags", "user": false, "write": "setDrawMode"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "texture", "notify": "textureChanged", "read": "texture", "required": false, "scriptable": true, "stored": true, "type": "QImage", "user": false, "write": "setTexture"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "textureFile", "notify": "textureFileChanged", "read": "textureFile", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setTextureFile"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "wireframeColor", "notify": "wireframeColorChanged", "read": "wireframeColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setWireframeColor"}], "qualifiedClassName": "QSurface3DSeries", "signals": [{"access": "public", "arguments": [{"name": "proxy", "type": "QSurfaceDataProxy*"}], "name": "dataProxyChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "position", "type": "QPoint"}], "name": "selectedPointChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enable", "type": "bool"}], "name": "flatShadingEnabledChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enable", "type": "bool"}], "name": "flatShadingSupportedChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "mode", "type": "QSurface3DSeries::DrawFlags"}], "name": "drawModeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "image", "type": "QImage"}], "name": "textureChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "filename", "type": "QString"}], "name": "textureFileChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "name": "wireframeColorChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstract3DSeries"}]}], "inputFile": "qsurface3dseries.h", "outputRevision": 68}, {"classes": [{"className": "QSurfaceDataProxy", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "rowCount", "notify": "rowCountChanged", "read": "rowCount", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "columnCount", "notify": "columnCountChanged", "read": "columnCount", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "series", "notify": "seriesChanged", "read": "series", "required": false, "scriptable": true, "stored": true, "type": "QSurface3DSeries*", "user": false}], "qualifiedClassName": "QSurfaceDataProxy", "signals": [{"access": "public", "name": "arrayReset", "returnType": "void"}, {"access": "public", "arguments": [{"name": "startIndex", "type": "int"}, {"name": "count", "type": "int"}], "name": "rowsAdded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "startIndex", "type": "int"}, {"name": "count", "type": "int"}], "name": "rowsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "startIndex", "type": "int"}, {"name": "count", "type": "int"}], "name": "rowsRemoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "startIndex", "type": "int"}, {"name": "count", "type": "int"}], "name": "rowsInserted", "returnType": "void"}, {"access": "public", "arguments": [{"name": "rowIndex", "type": "int"}, {"name": "columnIndex", "type": "int"}], "name": "itemChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "count", "type": "int"}], "name": "rowCountChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "count", "type": "int"}], "name": "columnCountChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "series", "type": "QSurface3DSeries*"}], "name": "seriesChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractDataProxy"}]}], "inputFile": "qsurfacedataproxy.h", "outputRevision": 68}, {"classes": [{"className": "ScatterItemModelHandler", "object": true, "qualifiedClassName": "ScatterItemModelHandler", "slots": [{"access": "public", "arguments": [{"name": "topLeft", "type": "QModelIndex"}, {"name": "bottomRight", "type": "QModelIndex"}, {"name": "roles", "type": "QList<int>"}], "name": "handleDataChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "topLeft", "type": "QModelIndex"}, {"name": "bottomRight", "type": "QModelIndex"}], "isCloned": true, "name": "handleDataChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "parent", "type": "QModelIndex"}, {"name": "start", "type": "int"}, {"name": "end", "type": "int"}], "name": "handleRowsInserted", "returnType": "void"}, {"access": "public", "arguments": [{"name": "parent", "type": "QModelIndex"}, {"name": "start", "type": "int"}, {"name": "end", "type": "int"}], "name": "handleRowsRemoved", "returnType": "void"}], "superClasses": [{"access": "public", "name": "AbstractItemModelHandler"}]}], "inputFile": "scatteritemmodelhandler_p.h", "outputRevision": 68}, {"classes": [{"className": "SurfaceItemModelHandler", "object": true, "qualifiedClassName": "SurfaceItemModelHandler", "slots": [{"access": "public", "arguments": [{"name": "topLeft", "type": "QModelIndex"}, {"name": "bottomRight", "type": "QModelIndex"}, {"name": "roles", "type": "QList<int>"}], "name": "handleDataChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "topLeft", "type": "QModelIndex"}, {"name": "bottomRight", "type": "QModelIndex"}], "isCloned": true, "name": "handleDataChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "AbstractItemModelHandler"}]}], "inputFile": "surfaceitemmodelhandler_p.h", "outputRevision": 68}, {"classes": [{"className": "Q3DInputHandler", "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "rotationEnabled", "notify": "rotationEnabledChanged", "read": "isRotationEnabled", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setRotationEnabled"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "zoomEnabled", "notify": "zoomEnabledChanged", "read": "isZoomEnabled", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setZoomEnabled"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "selectionEnabled", "notify": "selection<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "read": "isSelectionEnabled", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setSelectionEnabled"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "zoomAtTargetEnabled", "notify": "zoomAtTargetEnabledChanged", "read": "isZoomAtTargetEnabled", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setZoomAtTargetEnabled"}], "qualifiedClassName": "Q3DInputHandler", "signals": [{"access": "public", "arguments": [{"name": "enable", "type": "bool"}], "name": "rotationEnabledChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enable", "type": "bool"}], "name": "zoomEnabledChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enable", "type": "bool"}], "name": "selection<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enable", "type": "bool"}], "name": "zoomAtTargetEnabledChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstract3DInputHandler"}]}], "inputFile": "q3dinputhandler.h", "outputRevision": 68}, {"classes": [{"className": "QAbstract3DInputHandler", "enums": [{"isClass": false, "isFlag": false, "name": "InputView", "values": ["InputViewNone", "InputViewOnPrimary", "InputViewOnSecondary"]}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "inputView", "notify": "inputViewChanged", "read": "inputView", "required": false, "scriptable": true, "stored": true, "type": "QAbstract3DInputHandler::InputView", "user": false, "write": "setInputView"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "inputPosition", "notify": "positionChanged", "read": "inputPosition", "required": false, "scriptable": true, "stored": true, "type": "QPoint", "user": false, "write": "setInputPosition"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "scene", "notify": "sceneChanged", "read": "scene", "required": false, "scriptable": true, "stored": true, "type": "Q3DScene*", "user": false, "write": "setScene"}], "qualifiedClassName": "QAbstract3DInputHandler", "signals": [{"access": "public", "arguments": [{"name": "position", "type": "QPoint"}], "name": "positionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "view", "type": "QAbstract3DInputHandler::InputView"}], "name": "inputViewChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "scene", "type": "Q3DScene*"}], "name": "sceneChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "position", "type": "QPoint"}], "name": "handleSelection", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qabstract3dinputhandler.h", "outputRevision": 68}, {"classes": [{"className": "QAbstract3DInputHandlerPrivate", "object": true, "qualifiedClassName": "QAbstract3DInputHandlerPrivate", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qabstract3dinputhandler_p.h", "outputRevision": 68}]
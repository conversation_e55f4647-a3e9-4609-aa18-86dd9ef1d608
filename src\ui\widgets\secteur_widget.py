"""
Widget pour la gestion des secteurs
"""

from qt_imports import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QPushButton, QMessageBox, Qt
from typing import List

from .base_widget import BaseWidget
from models.secteur import SecteurModel

class SecteurDialog(QDialog):
    """Dialogue pour ajouter/modifier un secteur"""
    
    def __init__(self, parent=None, secteur_data=None):
        super().__init__(parent)
        self.secteur_data = secteur_data
        self.init_ui()
        
        if secteur_data:
            self.load_data()
    
    def init_ui(self):
        """Initialise l'interface du dialogue"""
        self.setWindowTitle("Secteur")
        self.setModal(True)
        self.resize(400, 150)
        
        layout = QVBoxLayout(self)
        
        # Champ nom du secteur
        nom_layout = QHBoxLayout()
        nom_label = QLabel("Nom du secteur:")
        self.nom_input = QLineEdit()
        self.nom_input.setPlaceholderText("Entrez le nom du secteur")
        
        nom_layout.addWidget(nom_label)
        nom_layout.addWidget(self.nom_input)
        layout.addLayout(nom_layout)
        
        # Boutons
        button_layout = QHBoxLayout()
        
        self.save_button = QPushButton("Enregistrer")
        self.save_button.clicked.connect(self.accept)
        
        self.cancel_button = QPushButton("Annuler")
        self.cancel_button.clicked.connect(self.reject)
        
        button_layout.addStretch()
        button_layout.addWidget(self.save_button)
        button_layout.addWidget(self.cancel_button)
        
        layout.addLayout(button_layout)
        
        # Focus sur le champ nom
        self.nom_input.setFocus()
    
    def load_data(self):
        """Charge les données du secteur pour modification"""
        if self.secteur_data:
            self.nom_input.setText(self.secteur_data.get('nom_secteur', ''))
    
    def get_data(self):
        """Récupère les données saisies"""
        return {
            'nom_secteur': self.nom_input.text().strip()
        }
    
    def validate_data(self):
        """Valide les données saisies"""
        data = self.get_data()
        
        if not data['nom_secteur']:
            QMessageBox.warning(self, "Erreur", "Le nom du secteur est obligatoire.")
            return False
        
        return True
    
    def accept(self):
        """Valide et ferme le dialogue"""
        if self.validate_data():
            super().accept()

class SecteurWidget(BaseWidget):
    """Widget pour la gestion des secteurs"""
    
    def __init__(self):
        self.model = SecteurModel()
        super().__init__("Secteurs", ["ID", "Nom du Secteur"])
    
    def get_table_columns(self) -> List[str]:
        """Retourne les noms des colonnes de la base de données"""
        return ['id_secteur', 'nom_secteur']
    
    def refresh_data(self):
        """Actualise les données du tableau"""
        try:
            data = self.model.get_all()
            self.populate_table(data)
        except Exception as e:
            self.show_message("Erreur", f"Erreur lors du chargement des données: {str(e)}", "error")
    
    def add_item(self):
        """Ajoute un nouveau secteur"""
        dialog = SecteurDialog(self)
        
        try:
            # PyQt6
            dialog_accepted = dialog.exec() == QDialog.DialogCode.Accepted
        except AttributeError:
            # PySide6
            dialog_accepted = dialog.exec() == QDialog.Accepted

        if dialog_accepted:
            data = dialog.get_data()

            try:
                secteur_id = self.model.create(data['nom_secteur'])
                if secteur_id:
                    self.show_message("Succès", "Secteur ajouté avec succès.")
                    self.refresh_data()
                    self.data_changed.emit()
                else:
                    self.show_message("Erreur", "Erreur lors de l'ajout du secteur.", "error")
            except Exception as e:
                self.show_message("Erreur", f"Erreur lors de l'ajout: {str(e)}", "error")
    
    def edit_item(self):
        """Modifie le secteur sélectionné"""
        selected_data = self.get_selected_row_data()
        if not selected_data:
            self.show_message("Erreur", "Veuillez sélectionner un secteur à modifier.", "warning")
            return
        
        dialog = SecteurDialog(self, selected_data)
        
        try:
            # PyQt6
            dialog_accepted = dialog.exec() == QDialog.DialogCode.Accepted
        except AttributeError:
            # PySide6
            dialog_accepted = dialog.exec() == QDialog.Accepted

        if dialog_accepted:
            data = dialog.get_data()

            try:
                success = self.model.update(
                    selected_data['id_secteur'],
                    data['nom_secteur']
                )
                
                if success:
                    self.show_message("Succès", "Secteur modifié avec succès.")
                    self.refresh_data()
                    self.data_changed.emit()
                else:
                    self.show_message("Erreur", "Erreur lors de la modification du secteur.", "error")
            except Exception as e:
                self.show_message("Erreur", f"Erreur lors de la modification: {str(e)}", "error")
    
    def delete_item(self):
        """Supprime le secteur sélectionné"""
        selected_data = self.get_selected_row_data()
        if not selected_data:
            self.show_message("Erreur", "Veuillez sélectionner un secteur à supprimer.", "warning")
            return
        
        reply = QMessageBox.question(
            self, 
            "Confirmation", 
            f"Êtes-vous sûr de vouloir supprimer le secteur '{selected_data['nom_secteur']}'?\n"
            "Cette action supprimera également toutes les unités associées.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                success = self.model.delete(selected_data['id_secteur'])
                
                if success:
                    self.show_message("Succès", "Secteur supprimé avec succès.")
                    self.refresh_data()
                    self.data_changed.emit()
                else:
                    self.show_message("Erreur", "Erreur lors de la suppression du secteur.", "error")
            except Exception as e:
                self.show_message("Erreur", f"Erreur lors de la suppression: {str(e)}", "error")

#!/usr/bin/env python3
"""
Script pour nettoyer toute la redondance dans la base de données
"""

import sys
import os

# Ajouter le répertoire src au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def clean_categories():
    """Nettoie les catégories vides/invalides"""
    print("=== Nettoyage des catégories ===")
    
    try:
        from database.connection import db_connection
        
        if not db_connection.connect():
            print("Erreur de connexion")
            return False
        
        # Supprimer les catégories vides
        query = "DELETE FROM CategoriePiece WHERE nom_categorie = '' OR nom_categorie IS NULL"
        success = db_connection.execute_update(query)
        
        if success:
            print("✅ Catégories vides supprimées")
            
            # Vérifier le résultat
            query_check = "SELECT COUNT(*) as count FROM CategoriePiece WHERE nom_categorie = '' OR nom_categorie IS NULL"
            result = db_connection.execute_query(query_check)
            if result and result[0]['count'] == 0:
                print("✅ Toutes les catégories vides ont été supprimées")
            
            # Afficher les catégories restantes
            query_remaining = "SELECT * FROM CategoriePiece"
            remaining = db_connection.execute_query(query_remaining)
            print(f"Catégories restantes: {len(remaining)}")
            for cat in remaining:
                print(f"  ID: {cat['id_categorie']}, Nom: '{cat['nom_categorie']}'")
        
        db_connection.disconnect()
        return success
        
    except Exception as e:
        print(f"Erreur: {e}")
        return False

def clean_secteur_duplicates():
    """Nettoie les doublons de secteurs"""
    print("\n=== Nettoyage des doublons de secteurs ===")
    
    try:
        from database.connection import db_connection
        
        if not db_connection.connect():
            print("Erreur de connexion")
            return False
        
        # Trouver les doublons
        query_duplicates = """
        SELECT nom_secteur, COUNT(*) as count, GROUP_CONCAT(id_secteur) as ids
        FROM Secteur 
        GROUP BY nom_secteur 
        HAVING COUNT(*) > 1
        """
        duplicates = db_connection.execute_query(query_duplicates)
        
        if not duplicates:
            print("✅ Aucun doublon de secteur trouvé")
            return True
        
        print(f"Doublons trouvés: {len(duplicates)}")
        
        for dup in duplicates:
            nom = dup['nom_secteur']
            ids = dup['ids'].split(',')
            count = dup['count']
            
            print(f"\nSecteur '{nom}' - {count} occurrences (IDs: {ids})")
            
            # Garder le premier ID, supprimer les autres
            keep_id = ids[0]
            delete_ids = ids[1:]
            
            print(f"  Garder ID: {keep_id}")
            print(f"  Supprimer IDs: {delete_ids}")
            
            # Mettre à jour les unités pour pointer vers le secteur conservé
            for delete_id in delete_ids:
                update_query = "UPDATE Unite SET id_secteur = %s WHERE id_secteur = %s"
                db_connection.execute_update(update_query, (keep_id, delete_id))
                print(f"    Unités mises à jour pour pointer vers {keep_id}")
            
            # Supprimer les secteurs en double
            for delete_id in delete_ids:
                delete_query = "DELETE FROM Secteur WHERE id_secteur = %s"
                db_connection.execute_update(delete_query, (delete_id,))
                print(f"    Secteur {delete_id} supprimé")
        
        # Vérifier le résultat
        query_check = """
        SELECT nom_secteur, COUNT(*) as count
        FROM Secteur 
        GROUP BY nom_secteur 
        HAVING COUNT(*) > 1
        """
        remaining_duplicates = db_connection.execute_query(query_check)
        
        if not remaining_duplicates:
            print("✅ Tous les doublons de secteurs ont été supprimés")
        else:
            print(f"⚠️ {len(remaining_duplicates)} doublons restants")
        
        db_connection.disconnect()
        return True
        
    except Exception as e:
        print(f"Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False

def clean_unite_duplicates():
    """Nettoie les doublons d'unités"""
    print("\n=== Nettoyage des doublons d'unités ===")
    
    try:
        from database.connection import db_connection
        
        if not db_connection.connect():
            print("Erreur de connexion")
            return False
        
        # Trouver les doublons (même nom ET même secteur)
        query_duplicates = """
        SELECT nom_unite, id_secteur, COUNT(*) as count, GROUP_CONCAT(id_unite) as ids
        FROM Unite 
        GROUP BY nom_unite, id_secteur 
        HAVING COUNT(*) > 1
        """
        duplicates = db_connection.execute_query(query_duplicates)
        
        if not duplicates:
            print("✅ Aucun doublon d'unité trouvé")
            return True
        
        print(f"Doublons trouvés: {len(duplicates)}")
        
        for dup in duplicates:
            nom = dup['nom_unite']
            secteur_id = dup['id_secteur']
            ids = dup['ids'].split(',')
            count = dup['count']
            
            print(f"\nUnité '{nom}' (Secteur {secteur_id}) - {count} occurrences (IDs: {ids})")
            
            # Garder le premier ID, supprimer les autres
            keep_id = ids[0]
            delete_ids = ids[1:]
            
            print(f"  Garder ID: {keep_id}")
            print(f"  Supprimer IDs: {delete_ids}")
            
            # Mettre à jour les consommations pour pointer vers l'unité conservée
            for delete_id in delete_ids:
                update_query = "UPDATE Consommation SET id_unite = %s WHERE id_unite = %s"
                db_connection.execute_update(update_query, (keep_id, delete_id))
                print(f"    Consommations mises à jour pour pointer vers {keep_id}")
            
            # Supprimer les unités en double
            for delete_id in delete_ids:
                delete_query = "DELETE FROM Unite WHERE id_unite = %s"
                db_connection.execute_update(delete_query, (delete_id,))
                print(f"    Unité {delete_id} supprimée")
        
        # Vérifier le résultat
        query_check = """
        SELECT nom_unite, id_secteur, COUNT(*) as count
        FROM Unite 
        GROUP BY nom_unite, id_secteur 
        HAVING COUNT(*) > 1
        """
        remaining_duplicates = db_connection.execute_query(query_check)
        
        if not remaining_duplicates:
            print("✅ Tous les doublons d'unités ont été supprimés")
        else:
            print(f"⚠️ {len(remaining_duplicates)} doublons restants")
        
        db_connection.disconnect()
        return True
        
    except Exception as e:
        print(f"Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_final_stats():
    """Affiche les statistiques finales après nettoyage"""
    print("\n=== Statistiques finales ===")
    
    try:
        from models.secteur import SecteurModel
        from models.unite import UniteModel
        from models.categorie_piece import CategoriePieceModel
        from models.piece import PieceModel
        from models.periode import PeriodeModel
        from models.consommation import ConsommationModel
        
        secteur_model = SecteurModel()
        unite_model = UniteModel()
        categorie_model = CategoriePieceModel()
        piece_model = PieceModel()
        periode_model = PeriodeModel()
        consommation_model = ConsommationModel()
        
        secteurs = secteur_model.get_all()
        unites = unite_model.get_all()
        categories = categorie_model.get_all()
        pieces = piece_model.get_all()
        periodes = periode_model.get_all()
        consommations = consommation_model.get_all()
        
        print(f"Secteurs: {len(secteurs)} (nettoyés)")
        print(f"Unités: {len(unites)} (nettoyées)")
        print(f"Catégories: {len(categories)} (nettoyées)")
        print(f"Pièces: {len(pieces)}")
        print(f"Périodes: {len(periodes)}")
        print(f"Consommations: {len(consommations)}")
        
        # Vérifier qu'il n'y a plus de doublons
        from collections import Counter
        
        # Secteurs
        noms_secteurs = [s.get('nom_secteur', '') for s in secteurs]
        doublons_secteurs = [nom for nom, count in Counter(noms_secteurs).items() if count > 1]
        if doublons_secteurs:
            print(f"⚠️ Doublons secteurs restants: {doublons_secteurs}")
        else:
            print("✅ Aucun doublon de secteur")
        
        # Unités (par nom et secteur)
        unites_keys = [(u.get('nom_unite', ''), u.get('id_secteur', '')) for u in unites]
        doublons_unites = [key for key, count in Counter(unites_keys).items() if count > 1]
        if doublons_unites:
            print(f"⚠️ Doublons unités restants: {len(doublons_unites)}")
        else:
            print("✅ Aucun doublon d'unité")
        
        # Catégories valides
        categories_valides = [c for c in categories if c.get('nom_categorie') and c.get('nom_categorie').strip()]
        print(f"✅ Catégories valides: {len(categories_valides)}")
        
    except Exception as e:
        print(f"Erreur: {e}")

def main():
    """Fonction principale"""
    print("=== Nettoyage complet de la redondance ===\n")
    
    response = input("Voulez-vous nettoyer toute la redondance de la base de données? (o/n): ")
    if response.lower() not in ['o', 'oui', 'y', 'yes']:
        print("Opération annulée.")
        return
    
    print("\n🚀 Début du nettoyage...")
    
    # Étape 1: Nettoyer les catégories
    success1 = clean_categories()
    
    # Étape 2: Nettoyer les doublons de secteurs
    success2 = clean_secteur_duplicates()
    
    # Étape 3: Nettoyer les doublons d'unités
    success3 = clean_unite_duplicates()
    
    # Afficher les statistiques finales
    show_final_stats()
    
    if success1 and success2 and success3:
        print("\n✅ Nettoyage terminé avec succès!")
        print("\nLa base de données a été optimisée:")
        print("- Catégories vides supprimées")
        print("- Doublons de secteurs fusionnés")
        print("- Doublons d'unités fusionnés")
        print("- Relations préservées")
        print("\nVous pouvez maintenant relancer l'application pour voir les améliorations.")
    else:
        print("\n⚠️ Le nettoyage a rencontré des problèmes")

if __name__ == "__main__":
    main()

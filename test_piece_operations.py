#!/usr/bin/env python3
"""
Test des opérations CRUD pour les pièces
"""

import sys
import os

# Ajouter le répertoire src au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_piece_crud():
    """Teste les opérations CRUD pour les pièces"""
    print("=== Test des opérations CRUD pour les pièces ===")
    
    try:
        from models.piece import PieceModel
        from models.categorie_piece import CategoriePieceModel
        
        piece_model = PieceModel()
        categorie_model = CategoriePieceModel()
        
        # Vérifier les catégories disponibles
        categories = categorie_model.get_all()
        print(f"Catégories disponibles: {len(categories)}")
        for cat in categories[:5]:
            print(f"  - ID: {cat['id_categorie']}, Nom: '{cat['nom_categorie']}'")
        
        if not categories:
            print("❌ Aucune catégorie trouvée")
            return False
        
        # Test de création d'une pièce
        print("\n--- Test de création ---")
        test_piece_name = "Test Pièce CRUD"
        categorie_id = categories[0]['id_categorie']  # Utiliser la première catégorie
        
        piece_id = piece_model.create(test_piece_name, categorie_id)
        if piece_id:
            print(f"✅ Pièce créée avec ID: {piece_id}")
            
            # Test de lecture
            print("\n--- Test de lecture ---")
            piece = piece_model.get_by_id(piece_id)
            if piece:
                print(f"✅ Pièce lue: {piece}")
                
                # Test de mise à jour
                print("\n--- Test de mise à jour ---")
                new_name = "Test Pièce CRUD Modifiée"
                success = piece_model.update(piece_id, new_name, categorie_id)
                if success:
                    print("✅ Pièce mise à jour avec succès")
                    
                    # Vérifier la mise à jour
                    updated_piece = piece_model.get_by_id(piece_id)
                    if updated_piece and updated_piece['nom_piece'] == new_name:
                        print(f"✅ Mise à jour vérifiée: {updated_piece['nom_piece']}")
                    else:
                        print("❌ Erreur de vérification de la mise à jour")
                    
                    # Test de suppression
                    print("\n--- Test de suppression ---")
                    success = piece_model.delete(piece_id)
                    if success:
                        print("✅ Pièce supprimée avec succès")
                        
                        # Vérifier la suppression
                        deleted_piece = piece_model.get_by_id(piece_id)
                        if not deleted_piece:
                            print("✅ Suppression vérifiée")
                            return True
                        else:
                            print("❌ Erreur: la pièce existe encore après suppression")
                    else:
                        print("❌ Erreur lors de la suppression")
                else:
                    print("❌ Erreur lors de la mise à jour")
            else:
                print("❌ Erreur lors de la lecture")
        else:
            print("❌ Erreur lors de la création")
        
        return False
        
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_piece_display():
    """Teste l'affichage des pièces avec catégories"""
    print("\n=== Test de l'affichage des pièces ===")
    
    try:
        from models.piece import PieceModel
        
        piece_model = PieceModel()
        pieces = piece_model.get_all()
        
        print(f"Nombre total de pièces: {len(pieces)}")
        
        if pieces:
            print("\nPremières pièces avec catégories:")
            for i, piece in enumerate(pieces[:5]):
                id_piece = piece.get('id_piece', 'N/A')
                nom_piece = piece.get('nom_piece', 'N/A')
                nom_categorie = piece.get('nom_categorie', 'N/A')
                
                print(f"  {i+1}. ID: {id_piece}, Nom: {nom_piece}, Catégorie: {nom_categorie}")
            
            # Vérifier que les colonnes attendues sont présentes
            expected_columns = ['id_piece', 'nom_piece', 'nom_categorie']
            if pieces:
                available_columns = list(pieces[0].keys())
                print(f"\nColonnes disponibles: {available_columns}")
                
                missing_columns = [col for col in expected_columns if col not in available_columns]
                if missing_columns:
                    print(f"❌ Colonnes manquantes: {missing_columns}")
                else:
                    print("✅ Toutes les colonnes attendues sont présentes")
                
                # Vérifier qu'id_categorie n'est pas affiché (sera masqué dans l'interface)
                if 'id_categorie' in available_columns:
                    print("ℹ️ id_categorie présent dans les données (sera masqué dans l'interface)")
                else:
                    print("ℹ️ id_categorie non présent dans les données")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test d'affichage: {e}")
        return False

def main():
    """Fonction principale"""
    print("=== Test des fonctionnalités des pièces ===\n")
    
    success1 = test_piece_display()
    success2 = test_piece_crud()
    
    if success1 and success2:
        print("\n✅ Tous les tests sont passés avec succès!")
        print("\nL'onglet Pièces dans l'application devrait maintenant:")
        print("- Masquer la colonne 'id_categorie'")
        print("- Afficher les noms de catégories")
        print("- Permettre d'ajouter des pièces avec autocomplétion")
        print("- Permettre de modifier des pièces")
        print("- Permettre de supprimer des pièces")
    else:
        print("\n❌ Certains tests ont échoué")

if __name__ == "__main__":
    main()

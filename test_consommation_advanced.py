#!/usr/bin/env python3
"""
Test des fonctionnalités avancées de l'onglet Consommations
"""

import sys
import os
from datetime import date

# Ajouter le répertoire src au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_consommation_creation():
    """Teste la création d'une consommation avec les nouveaux formulaires"""
    print("=== Test de création de consommation ===")
    
    try:
        from models.consommation import ConsommationModel
        from models.unite import UniteModel
        from models.piece import PieceModel
        from models.periode import PeriodeModel
        
        # Modèles
        consommation_model = ConsommationModel()
        unite_model = UniteModel()
        piece_model = PieceModel()
        periode_model = PeriodeModel()
        
        # Vérifier les données disponibles
        unites = unite_model.get_all()
        pieces = piece_model.get_all()
        periodes = periode_model.get_all()
        
        print(f"Unités disponibles: {len(unites)}")
        if unites:
            print(f"  Exemple: {unites[0].get('nom_unite')}")
        
        print(f"Pièces disponibles: {len(pieces)}")
        if pieces:
            print(f"  Exemple: {pieces[0].get('nom_piece')}")
        
        print(f"Périodes disponibles: {len(periodes)}")
        if periodes:
            print(f"  Exemple: {periodes[0].get('date_periode')}")
        
        # Test de création d'une consommation
        if unites and pieces:
            unite_id = unites[0]['id_unite']
            piece_id = pieces[0]['id_piece']
            
            # Créer une période de test
            test_date = date(2024, 12, 1)
            periode_id = periode_model.create(test_date, 12)
            
            if periode_id:
                print(f"Période de test créée: ID {periode_id}")
                
                # Créer une consommation de test
                consommation_id = consommation_model.create(
                    unite_id, piece_id, periode_id, 100, 75
                )
                
                if consommation_id:
                    print(f"✅ Consommation de test créée: ID {consommation_id}")
                    
                    # Vérifier l'affichage avec détails
                    consommations = consommation_model.get_all()
                    if consommations:
                        print("Exemple de consommation avec détails:")
                        exemple = consommations[-1]  # Dernière créée
                        for key, value in exemple.items():
                            print(f"  {key}: {value}")
                    
                    # Nettoyer (supprimer la consommation de test)
                    consommation_model.delete(consommation_id)
                    periode_model.delete(periode_id)
                    print("✅ Données de test nettoyées")
                    
                    return True
                else:
                    print("❌ Erreur de création de consommation")
            else:
                print("❌ Erreur de création de période")
        else:
            print("❌ Pas assez de données pour le test")
        
        return False
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_display_format():
    """Teste le format d'affichage des consommations"""
    print("\n=== Test du format d'affichage ===")
    
    try:
        from models.consommation import ConsommationModel
        
        consommation_model = ConsommationModel()
        consommations = consommation_model.get_all()
        
        print(f"Nombre de consommations: {len(consommations)}")
        
        if consommations:
            print("\nPremière consommation - structure complète:")
            first_consommation = consommations[0]
            
            # Vérifier les champs attendus
            expected_fields = [
                'id_consommation', 'nom_unite', 'nom_secteur', 'nom_piece', 
                'nom_categorie', 'date_periode', 'nombre_attribue', 
                'nombre_consomme', 'reste_non_consomme'
            ]
            
            available_fields = list(first_consommation.keys())
            print(f"Champs disponibles: {available_fields}")
            
            missing_fields = [field for field in expected_fields if field not in available_fields]
            if missing_fields:
                print(f"⚠️ Champs manquants: {missing_fields}")
            else:
                print("✅ Tous les champs attendus sont présents")
            
            # Afficher les valeurs
            print("\nValeurs de la première consommation:")
            for field in expected_fields:
                if field in first_consommation:
                    value = first_consommation[field]
                    print(f"  {field}: {value}")
            
            # Vérifier que les noms sont affichés au lieu des IDs
            has_names = (
                'nom_unite' in first_consommation and 
                'nom_piece' in first_consommation and
                first_consommation.get('nom_unite') and
                first_consommation.get('nom_piece')
            )
            
            if has_names:
                print("✅ Les noms sont correctement affichés")
            else:
                print("❌ Problème d'affichage des noms")
            
            return has_names
        else:
            print("ℹ️ Aucune consommation pour tester l'affichage")
            return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_form_data():
    """Teste les données disponibles pour les formulaires"""
    print("\n=== Test des données pour formulaires ===")
    
    try:
        from models.unite import UniteModel
        from models.piece import PieceModel
        from models.periode import PeriodeModel
        
        unite_model = UniteModel()
        piece_model = PieceModel()
        periode_model = PeriodeModel()
        
        # Test des unités pour auto-complétion
        unites = unite_model.get_all()
        print(f"Unités pour auto-complétion: {len(unites)}")
        if unites:
            print("Exemples d'unités:")
            for i, unite in enumerate(unites[:5]):
                nom_unite = unite.get('nom_unite', 'N/A')
                nom_secteur = unite.get('nom_secteur', 'N/A')
                print(f"  {i+1}. {nom_unite} ({nom_secteur})")
        
        # Test des pièces pour auto-complétion
        pieces = piece_model.get_all()
        print(f"\nPièces pour auto-complétion: {len(pieces)}")
        if pieces:
            print("Exemples de pièces:")
            for i, piece in enumerate(pieces[:5]):
                nom_piece = piece.get('nom_piece', 'N/A')
                nom_categorie = piece.get('nom_categorie', 'N/A')
                print(f"  {i+1}. {nom_piece} ({nom_categorie})")
        
        # Test des périodes
        periodes = periode_model.get_all()
        print(f"\nPériodes disponibles: {len(periodes)}")
        if periodes:
            print("Exemples de périodes:")
            for i, periode in enumerate(periodes[:3]):
                date_periode = periode.get('date_periode', 'N/A')
                mois = periode.get('mois', 'N/A')
                print(f"  {i+1}. {date_periode} (Mois {mois})")
        
        # Vérifier que les données sont prêtes pour les formulaires
        ready = len(unites) > 0 and len(pieces) > 0
        
        if ready:
            print("\n✅ Toutes les données sont prêtes pour les formulaires")
        else:
            print("\n⚠️ Données insuffisantes pour les formulaires")
        
        return ready
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def main():
    """Fonction principale"""
    print("=== Test des fonctionnalités avancées de Consommations ===\n")
    
    results = []
    results.append(test_form_data())
    results.append(test_display_format())
    results.append(test_consommation_creation())
    
    success_count = sum(results)
    total_count = len(results)
    
    print(f"\n=== Résultats ===")
    print(f"Tests réussis: {success_count}/{total_count}")
    
    if success_count == total_count:
        print("\n✅ Toutes les fonctionnalités avancées sont opérationnelles!")
        print("\nL'onglet Consommations dispose maintenant de:")
        print("- ✅ Formulaire avec noms d'unités (auto-complétion)")
        print("- ✅ Formulaire avec noms de pièces (auto-complétion)")
        print("- ✅ Sélecteur de date pour les périodes")
        print("- ✅ Calcul automatique du reste")
        print("- ✅ Affichage sans IDs techniques")
        print("- ✅ Validation des données")
        print("- ✅ Création automatique de périodes")
    else:
        print(f"\n⚠️ {total_count - success_count} test(s) ont échoué")
    
    print("\nVous pouvez maintenant tester dans l'application:")
    print("1. Aller dans l'onglet 'Consommations'")
    print("2. Cliquer sur 'Ajouter'")
    print("3. Utiliser les listes déroulantes pour Unité et Pièce")
    print("4. Saisir une date et sélectionner un mois")
    print("5. Entrer les quantités et voir le calcul automatique")

if __name__ == "__main__":
    main()

"""
Modèle pour la table Unite
"""

from typing import List, Dict, Any, Optional
from .base_model import BaseModel

class UniteModel(BaseModel):
    """Modèle pour la gestion des unités"""
    
    @property
    def table_name(self) -> str:
        return "Unite"
    
    @property
    def primary_key(self) -> str:
        return "id_unite"
    
    def create(self, nom_unite: str, id_secteur: int) -> Optional[int]:
        """
        Crée une nouvelle unité
        
        Args:
            nom_unite: Nom de l'unité
            id_secteur: ID du secteur
            
        Returns:
            ID de l'unité créée ou None en cas d'erreur
        """
        query = "INSERT INTO Unite (nom_unite, id_secteur) VALUES (%s, %s)"
        if self.db.execute_update(query, (nom_unite, id_secteur)):
            return self.db.get_last_insert_id()
        return None
    
    def update(self, id_unite: int, nom_unite: str, id_secteur: int) -> bool:
        """
        Met à jour une unité
        
        Args:
            id_unite: ID de l'unité
            nom_unite: Nouveau nom de l'unité
            id_secteur: Nouvel ID du secteur
            
        Returns:
            True si la mise à jour est réussie
        """
        query = "UPDATE Unite SET nom_unite = %s, id_secteur = %s WHERE id_unite = %s"
        return self.db.execute_update(query, (nom_unite, id_secteur, id_unite))
    
    def get_all(self) -> List[Dict[str, Any]]:
        """
        Récupère toutes les unités avec les informations du secteur
        (Override de la méthode de base pour inclure les noms de secteurs)

        Returns:
            Liste des unités avec secteur
        """
        return self.get_all_with_secteur()

    def get_all_with_secteur(self) -> List[Dict[str, Any]]:
        """
        Récupère toutes les unités avec les informations du secteur
        
        Returns:
            Liste des unités avec secteur
        """
        query = """
        SELECT u.id_unite, u.nom_unite, u.id_secteur, s.nom_secteur
        FROM Unite u
        LEFT JOIN Secteur s ON u.id_secteur = s.id_secteur
        ORDER BY u.nom_unite
        """
        result = self.db.execute_query(query)
        return result or []
    
    def get_by_secteur(self, id_secteur: int) -> List[Dict[str, Any]]:
        """
        Récupère les unités d'un secteur spécifique
        
        Args:
            id_secteur: ID du secteur
            
        Returns:
            Liste des unités du secteur
        """
        query = "SELECT * FROM Unite WHERE id_secteur = %s ORDER BY nom_unite"
        result = self.db.execute_query(query, (id_secteur,))
        return result or []
    
    def get_unites_for_combobox(self) -> List[Dict[str, Any]]:
        """
        Récupère les unités formatées pour les combobox
        
        Returns:
            Liste des unités avec id et nom
        """
        return self.get_all_with_secteur()
    
    def search_by_name(self, name: str) -> List[Dict[str, Any]]:
        """
        Recherche les unités par nom
        
        Args:
            name: Nom à rechercher
            
        Returns:
            Liste des unités correspondantes
        """
        return self.search(name, ['nom_unite'])

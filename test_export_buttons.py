#!/usr/bin/env python3
"""
Test pour vérifier la présence des boutons d'export dans l'onglet Consommations
"""

import sys
import os
import tkinter as tk
from tkinter import ttk

# Ajouter le répertoire src au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_consommation_frame():
    """Teste la création du frame Consommations avec boutons d'export"""
    print("=== Test des boutons d'export dans ConsommationFrame ===")
    
    try:
        from models.consommation import ConsommationModel
        from models.unite import UniteModel
        from models.piece import PieceModel
        from models.periode import PeriodeModel
        
        # Créer une fenêtre de test
        root = tk.Tk()
        root.title("Test Export Buttons")
        root.geometry("800x600")
        
        # Créer les modèles
        consommation_model = ConsommationModel()
        unite_model = UniteModel()
        piece_model = PieceModel()
        periode_model = PeriodeModel()
        
        # Importer la classe ConsommationFrame
        sys.path.insert(0, os.path.dirname(__file__))
        from app_tkinter import ConsommationFrame
        
        # Créer le frame de consommation
        consommation_frame = ConsommationFrame(
            root, 
            "Consommations", 
            consommation_model,
            unite_model,
            piece_model,
            periode_model
        )
        consommation_frame.pack(fill='both', expand=True)
        
        # Vérifier la présence des boutons d'export
        print("Recherche des boutons d'export...")
        
        export_buttons_found = []
        
        def find_export_buttons(widget, level=0):
            """Recherche récursive des boutons d'export"""
            indent = "  " * level
            
            if isinstance(widget, ttk.Button):
                button_text = widget['text']
                print(f"{indent}Bouton trouvé: '{button_text}'")
                
                if any(keyword in button_text for keyword in ['Export', 'CSV', 'Excel', 'Personnalisé']):
                    export_buttons_found.append(button_text)
                    print(f"{indent}  ✅ BOUTON D'EXPORT DÉTECTÉ!")
            
            # Rechercher dans les enfants
            try:
                for child in widget.winfo_children():
                    find_export_buttons(child, level + 1)
            except:
                pass
        
        # Lancer la recherche
        find_export_buttons(consommation_frame)
        
        # Résultats
        print(f"\n=== RÉSULTATS ===")
        print(f"Boutons d'export trouvés: {len(export_buttons_found)}")
        
        for i, button_text in enumerate(export_buttons_found, 1):
            print(f"  {i}. {button_text}")
        
        if len(export_buttons_found) >= 3:
            print("✅ SUCCÈS: Tous les boutons d'export sont présents!")
        else:
            print("❌ PROBLÈME: Boutons d'export manquants!")
        
        # Vérifier l'ordre des colonnes
        print(f"\n=== VÉRIFICATION DE L'ORDRE DES COLONNES ===")
        
        if hasattr(consommation_frame, 'tree') and consommation_frame.tree['columns']:
            columns = consommation_frame.tree['columns']
            print(f"Colonnes trouvées: {columns}")
            
            expected_order = ['nom_secteur', 'nom_unite', 'nom_piece', 'nom_categorie', 'date_periode', 
                            'nombre_attribue', 'nombre_consomme', 'reste_non_consomme']
            
            if list(columns) == expected_order:
                print("✅ SUCCÈS: Ordre des colonnes correct!")
                print("   Secteur → Unité → Pièce → Catégorie → Date → Quantités")
            else:
                print("❌ PROBLÈME: Ordre des colonnes incorrect!")
                print(f"   Attendu: {expected_order}")
                print(f"   Trouvé:  {list(columns)}")
        
        # Afficher la fenêtre pour inspection visuelle
        print(f"\n=== INSPECTION VISUELLE ===")
        print("Fenêtre de test ouverte pour inspection visuelle.")
        print("Vérifiez manuellement la présence des boutons d'export.")
        print("Fermez la fenêtre pour terminer le test.")
        
        # Centrer la fenêtre
        root.update_idletasks()
        x = (root.winfo_screenwidth() // 2) - (800 // 2)
        y = (root.winfo_screenheight() // 2) - (600 // 2)
        root.geometry(f"800x600+{x}+{y}")
        
        # Lancer la boucle d'événements
        root.mainloop()
        
        return len(export_buttons_found) >= 3
        
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_consommation_frame()
    
    if success:
        print("\n🎉 TEST RÉUSSI: Les boutons d'export sont présents!")
    else:
        print("\n❌ TEST ÉCHOUÉ: Problème avec les boutons d'export!")

<?xml version="1.0" encoding="UTF-8"?>
<!--
// Copyright (C) 2017 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
-->

<typesystem package="PySide6.Qt3DExtras">
    <load-typesystem name="typesystem_3drender.xml" generate="no"/>
    <namespace-type name="Qt3DExtras">
        <object-type name="QAbstractCameraController">
            <value-type name="InputState"/>
        </object-type>
        <object-type name="QAbstractSpriteSheet"/>
        <object-type name="QConeGeometry"/>
        <object-type name="QConeMesh"/>
        <object-type name="QConeGeometryView"/>
        <object-type name="QCuboidGeometry"/>
        <object-type name="QCuboidGeometryView"/>
        <object-type name="QCuboidMesh"/>
        <object-type name="QCylinderGeometry"/>
        <object-type name="QCylinderGeometryView"/>
        <object-type name="QCylinderMesh"/>
        <object-type name="QDiffuseMapMaterial"/>
        <object-type name="QDiffuseSpecularMaterial"/>
        <object-type name="QDiffuseSpecularMapMaterial"/>
        <object-type name="QExtrudedTextGeometry"/>
        <object-type name="QExtrudedTextMesh"/>
        <object-type name="QFirstPersonCameraController"/>
        <object-type name="QForwardRenderer"/>
        <object-type name="QGoochMaterial"/>
        <object-type name="QMetalRoughMaterial"/>
        <object-type name="QMorphPhongMaterial"/>
        <object-type name="QNormalDiffuseMapAlphaMaterial"/>
        <object-type name="QNormalDiffuseMapMaterial"/>
        <object-type name="QNormalDiffuseSpecularMapMaterial"/>
        <object-type name="QOrbitCameraController"/>
        <object-type name="QPerVertexColorMaterial"/>
        <object-type name="QPhongMaterial"/>
        <object-type name="QPhongAlphaMaterial"/>
        <object-type name="QPlaneGeometry"/>
        <object-type name="QPlaneGeometryView"/>
        <object-type name="QPlaneMesh"/>
        <object-type name="QSkyboxEntity"/>
        <object-type name="QSphereGeometry"/>
        <object-type name="QSphereGeometryView"/>
        <object-type name="QSphereMesh"/>
        <object-type name="QSpriteGrid"/>
        <object-type name="QSpriteSheet"/>
        <object-type name="QSpriteSheetItem"/>
        <object-type name="QText2DEntity"/>
        <object-type name="QTextureMaterial"/>
        <object-type name="QTorusGeometry"/>
        <object-type name="QTorusGeometryView"/>
        <object-type name="QTorusMesh"/>
        <object-type name="Qt3DWindow"/>
    </namespace-type>
</typesystem>

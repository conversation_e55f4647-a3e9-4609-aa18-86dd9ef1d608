"""
Modèle pour la table Consommation
"""

from typing import List, Dict, Any, Optional
from .base_model import BaseModel

class ConsommationModel(BaseModel):
    """Modèle pour la gestion des consommations"""
    
    @property
    def table_name(self) -> str:
        return "Consommation"
    
    @property
    def primary_key(self) -> str:
        return "id_consommation"
    
    def create(self, id_unite: int, id_piece: int, id_periode: int, 
               nombre_attribue: int, nombre_consomme: int = 0) -> Optional[int]:
        """
        Crée une nouvelle consommation
        
        Args:
            id_unite: ID de l'unité
            id_piece: ID de la pièce
            id_periode: ID de la période
            nombre_attribue: Nombre attribué
            nombre_consomme: Nombre consommé (défaut: 0)
            
        Returns:
            ID de la consommation créée ou None en cas d'erreur
        """
        query = """
        INSERT INTO Consommation (id_unite, id_piece, id_periode, nombre_attribue, nombre_consomme)
        VALUES (%s, %s, %s, %s, %s)
        """
        if self.db.execute_update(query, (id_unite, id_piece, id_periode, nombre_attribue, nombre_consomme)):
            return self.db.get_last_insert_id()
        return None
    
    def update(self, id_consommation: int, id_unite: int, id_piece: int, id_periode: int,
               nombre_attribue: int, nombre_consomme: int) -> bool:
        """
        Met à jour une consommation
        
        Args:
            id_consommation: ID de la consommation
            id_unite: ID de l'unité
            id_piece: ID de la pièce
            id_periode: ID de la période
            nombre_attribue: Nombre attribué
            nombre_consomme: Nombre consommé
            
        Returns:
            True si la mise à jour est réussie
        """
        query = """
        UPDATE Consommation 
        SET id_unite = %s, id_piece = %s, id_periode = %s, 
            nombre_attribue = %s, nombre_consomme = %s
        WHERE id_consommation = %s
        """
        return self.db.execute_update(query, (id_unite, id_piece, id_periode, 
                                            nombre_attribue, nombre_consomme, id_consommation))
    
    def get_all(self) -> List[Dict[str, Any]]:
        """
        Récupère toutes les consommations avec les détails des tables liées
        (Override de la méthode de base pour inclure tous les noms)

        Returns:
            Liste des consommations avec détails
        """
        return self.get_all_with_details()

    def get_all_with_details(self) -> List[Dict[str, Any]]:
        """
        Récupère toutes les consommations avec les détails des tables liées
        
        Returns:
            Liste des consommations avec détails
        """
        query = """
        SELECT c.id_consommation, c.nombre_attribue, c.nombre_consomme, c.reste_non_consomme,
               u.id_unite, u.nom_unite, s.nom_secteur,
               p.id_piece, p.nom_piece, cat.nom_categorie,
               per.id_periode, per.date_periode, per.mois
        FROM Consommation c
        LEFT JOIN Unite u ON c.id_unite = u.id_unite
        LEFT JOIN Secteur s ON u.id_secteur = s.id_secteur
        LEFT JOIN Piece p ON c.id_piece = p.id_piece
        LEFT JOIN CategoriePiece cat ON p.id_categorie = cat.id_categorie
        LEFT JOIN Periode per ON c.id_periode = per.id_periode
        ORDER BY per.date_periode DESC, u.nom_unite, p.nom_piece
        """
        result = self.db.execute_query(query)
        return result or []

    def get_by_unite(self, id_unite: int) -> List[Dict[str, Any]]:
        """
        Récupère les consommations d'une unité spécifique

        Args:
            id_unite: ID de l'unité

        Returns:
            Liste des consommations de l'unité
        """
        query = """
        SELECT c.*, u.nom_unite, p.nom_piece, per.date_periode
        FROM Consommation c
        LEFT JOIN Unite u ON c.id_unite = u.id_unite
        LEFT JOIN Piece p ON c.id_piece = p.id_piece
        LEFT JOIN Periode per ON c.id_periode = per.id_periode
        WHERE c.id_unite = %s
        ORDER BY per.date_periode DESC
        """
        result = self.db.execute_query(query, (id_unite,))
        return result or []

    def get_by_periode(self, id_periode: int) -> List[Dict[str, Any]]:
        """
        Récupère les consommations d'une période spécifique

        Args:
            id_periode: ID de la période

        Returns:
            Liste des consommations de la période
        """
        query = """
        SELECT c.*, u.nom_unite, p.nom_piece, per.date_periode
        FROM Consommation c
        LEFT JOIN Unite u ON c.id_unite = u.id_unite
        LEFT JOIN Piece p ON c.id_piece = p.id_piece
        LEFT JOIN Periode per ON c.id_periode = per.id_periode
        WHERE c.id_periode = %s
        ORDER BY u.nom_unite, p.nom_piece
        """
        result = self.db.execute_query(query, (id_periode,))
        return result or []

    def get_statistics(self) -> Dict[str, Any]:
        """
        Récupère les statistiques de consommation

        Returns:
            Dictionnaire avec les statistiques
        """
        query = """
        SELECT
            COUNT(*) as total_consommations,
            SUM(nombre_attribue) as total_attribue,
            SUM(nombre_consomme) as total_consomme,
            SUM(reste_non_consomme) as total_reste,
            AVG(nombre_attribue) as moyenne_attribue,
            AVG(nombre_consomme) as moyenne_consomme
        FROM Consommation
        """
        result = self.db.execute_query(query)
        return result[0] if result else {}

    def search_consommations(self, search_term: str) -> List[Dict[str, Any]]:
        """
        Recherche dans les consommations

        Args:
            search_term: Terme de recherche

        Returns:
            Liste des consommations correspondantes
        """
        query = """
        SELECT c.id_consommation, c.nombre_attribue, c.nombre_consomme, c.reste_non_consomme,
               u.nom_unite, s.nom_secteur, p.nom_piece, cat.nom_categorie,
               per.date_periode, per.mois
        FROM Consommation c
        LEFT JOIN Unite u ON c.id_unite = u.id_unite
        LEFT JOIN Secteur s ON u.id_secteur = s.id_secteur
        LEFT JOIN Piece p ON c.id_piece = p.id_piece
        LEFT JOIN CategoriePiece cat ON p.id_categorie = cat.id_categorie
        LEFT JOIN Periode per ON c.id_periode = per.id_periode
        WHERE u.nom_unite LIKE %s OR p.nom_piece LIKE %s OR s.nom_secteur LIKE %s
        ORDER BY per.date_periode DESC
        """
        search_pattern = f"%{search_term}%"
        result = self.db.execute_query(query, (search_pattern, search_pattern, search_pattern))
        return result or []

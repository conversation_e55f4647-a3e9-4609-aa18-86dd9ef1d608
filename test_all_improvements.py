#!/usr/bin/env python3
"""
Test de toutes les améliorations apportées à l'application
"""

import sys
import os

# Ajouter le répertoire src au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_secteur_operations():
    """Teste les opérations CRUD pour les secteurs"""
    print("=== Test Secteurs ===")
    
    try:
        from models.secteur import SecteurModel
        
        secteur_model = SecteurModel()
        
        # Test de création
        secteur_id = secteur_model.create("Test Secteur Interface")
        if secteur_id:
            print("✅ Création de secteur fonctionnelle")
            
            # Test de modification
            success = secteur_model.update(secteur_id, "Test Secteur Interface Modifié")
            if success:
                print("✅ Modification de secteur fonctionnelle")
            
            # Test de suppression
            success = secteur_model.delete(secteur_id)
            if success:
                print("✅ Suppression de secteur fonctionnelle")
            
            return True
        else:
            print("❌ Erreur de création de secteur")
            return False
            
    except Exception as e:
        print(f"❌ Erreur secteur: {e}")
        return False

def test_unite_operations():
    """Teste les opérations CRUD pour les unités"""
    print("\n=== Test Unités ===")
    
    try:
        from models.unite import UniteModel
        from models.secteur import SecteurModel
        
        unite_model = UniteModel()
        secteur_model = SecteurModel()
        
        # Récupérer un secteur existant
        secteurs = secteur_model.get_all()
        if not secteurs:
            print("❌ Aucun secteur disponible pour le test")
            return False
        
        secteur_id = secteurs[0]['id_secteur']
        
        # Test de création
        unite_id = unite_model.create("Test Unité Interface", secteur_id)
        if unite_id:
            print("✅ Création d'unité fonctionnelle")
            
            # Test de modification
            success = unite_model.update(unite_id, "Test Unité Interface Modifiée", secteur_id)
            if success:
                print("✅ Modification d'unité fonctionnelle")
            
            # Test de suppression
            success = unite_model.delete(unite_id)
            if success:
                print("✅ Suppression d'unité fonctionnelle")
            
            return True
        else:
            print("❌ Erreur de création d'unité")
            return False
            
    except Exception as e:
        print(f"❌ Erreur unité: {e}")
        return False

def test_piece_operations():
    """Teste les opérations CRUD pour les pièces"""
    print("\n=== Test Pièces ===")
    
    try:
        from models.piece import PieceModel
        from models.categorie_piece import CategoriePieceModel
        
        piece_model = PieceModel()
        categorie_model = CategoriePieceModel()
        
        # Récupérer une catégorie existante
        categories = categorie_model.get_all()
        valid_categories = [c for c in categories if c.get('nom_categorie')]
        
        if not valid_categories:
            print("❌ Aucune catégorie valide disponible pour le test")
            return False
        
        categorie_id = valid_categories[0]['id_categorie']
        
        # Test de création
        piece_id = piece_model.create("Test Pièce Interface", categorie_id)
        if piece_id:
            print("✅ Création de pièce fonctionnelle")
            
            # Test de modification
            success = piece_model.update(piece_id, "Test Pièce Interface Modifiée", categorie_id)
            if success:
                print("✅ Modification de pièce fonctionnelle")
            
            # Test de suppression
            success = piece_model.delete(piece_id)
            if success:
                print("✅ Suppression de pièce fonctionnelle")
            
            return True
        else:
            print("❌ Erreur de création de pièce")
            return False
            
    except Exception as e:
        print(f"❌ Erreur pièce: {e}")
        return False

def test_periode_operations():
    """Teste les opérations CRUD pour les périodes"""
    print("\n=== Test Périodes ===")
    
    try:
        from models.periode import PeriodeModel
        from datetime import date
        
        periode_model = PeriodeModel()
        
        # Test de création
        test_date = date(2024, 12, 1)
        periode_id = periode_model.create(test_date, 12)
        if periode_id:
            print("✅ Création de période fonctionnelle")
            
            # Test de modification
            new_date = date(2024, 12, 15)
            success = periode_model.update(periode_id, new_date, 12)
            if success:
                print("✅ Modification de période fonctionnelle")
            
            # Test de suppression
            success = periode_model.delete(periode_id)
            if success:
                print("✅ Suppression de période fonctionnelle")
            
            return True
        else:
            print("❌ Erreur de création de période")
            return False
            
    except Exception as e:
        print(f"❌ Erreur période: {e}")
        return False

def test_display_improvements():
    """Teste les améliorations d'affichage"""
    print("\n=== Test Améliorations d'affichage ===")
    
    try:
        from models.piece import PieceModel
        from models.unite import UniteModel
        
        # Test affichage pièces
        piece_model = PieceModel()
        pieces = piece_model.get_all()
        
        if pieces:
            first_piece = pieces[0]
            if 'nom_categorie' in first_piece and 'id_categorie' in first_piece:
                print("✅ Pièces: nom_categorie affiché, id_categorie disponible pour masquage")
            else:
                print("❌ Problème d'affichage des pièces")
        
        # Test affichage unités
        unite_model = UniteModel()
        unites = unite_model.get_all()
        
        if unites:
            first_unite = unites[0]
            if 'nom_secteur' in first_unite and 'id_secteur' in first_unite:
                print("✅ Unités: nom_secteur affiché, id_secteur disponible pour masquage")
            else:
                print("❌ Problème d'affichage des unités")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur affichage: {e}")
        return False

def main():
    """Fonction principale"""
    print("=== Test de toutes les améliorations ===\n")
    
    results = []
    results.append(test_secteur_operations())
    results.append(test_unite_operations())
    results.append(test_piece_operations())
    results.append(test_periode_operations())
    results.append(test_display_improvements())
    
    success_count = sum(results)
    total_count = len(results)
    
    print(f"\n=== Résultats ===")
    print(f"Tests réussis: {success_count}/{total_count}")
    
    if success_count == total_count:
        print("\n✅ Toutes les améliorations fonctionnent correctement!")
        print("\nL'application dispose maintenant de:")
        print("- ❌ Onglet Catégories masqué")
        print("- ✅ Boutons opérationnels dans tous les onglets")
        print("- ✅ Affichage sans redondance")
        print("- ✅ Auto-complétion dans les formulaires")
        print("- ✅ Validation des données")
        print("- ✅ Messages de confirmation")
    else:
        print(f"\n⚠️ {total_count - success_count} test(s) ont échoué")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Test pour vérifier les boutons d'export dans l'application principale
"""

import sys
import os
import tkinter as tk
from tkinter import ttk
import time

# Ajouter le répertoire src au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_main_app_export_buttons():
    """Lance l'application principale et vérifie les boutons d'export"""
    print("=== Test des boutons d'export dans l'application principale ===")
    
    try:
        # Importer l'application principale
        sys.path.insert(0, os.path.dirname(__file__))
        from app_tkinter import GestionMunApp
        
        # Créer l'application
        app = GestionMunApp()
        
        # Attendre que l'interface soit construite
        app.update_idletasks()
        time.sleep(1)
        
        print("Application principale lancée.")
        print("Recherche de l'onglet Consommations...")
        
        # Trouver l'onglet Consommations
        notebook = None
        consommation_frame = None
        
        def find_notebook_and_consommation(widget, level=0):
            nonlocal notebook, consommation_frame
            indent = "  " * level
            
            if isinstance(widget, ttk.Notebook):
                print(f"{indent}Notebook trouvé!")
                notebook = widget
                
                # Parcourir les onglets
                for i in range(notebook.index("end")):
                    tab_text = notebook.tab(i, "text")
                    print(f"{indent}  Onglet {i}: '{tab_text}'")
                    
                    if tab_text == "Consommations":
                        print(f"{indent}  ✅ Onglet Consommations trouvé à l'index {i}!")
                        consommation_frame = notebook.nametowidget(notebook.tabs()[i])
                        
                        # Sélectionner l'onglet Consommations
                        notebook.select(i)
                        print(f"{indent}  Onglet Consommations sélectionné.")
                        return
            
            # Rechercher dans les enfants
            try:
                for child in widget.winfo_children():
                    find_notebook_and_consommation(child, level + 1)
            except:
                pass
        
        # Lancer la recherche
        find_notebook_and_consommation(app)
        
        if consommation_frame is None:
            print("❌ ERREUR: Onglet Consommations non trouvé!")
            app.destroy()
            return False
        
        # Attendre un peu pour que l'onglet soit affiché
        app.update_idletasks()
        time.sleep(0.5)
        
        # Rechercher les boutons d'export dans l'onglet Consommations
        print("\nRecherche des boutons d'export dans l'onglet Consommations...")
        
        export_buttons_found = []
        
        def find_export_buttons_in_tab(widget, level=0):
            """Recherche récursive des boutons d'export dans l'onglet"""
            indent = "  " * level
            
            if isinstance(widget, ttk.Button):
                button_text = widget['text']
                print(f"{indent}Bouton trouvé: '{button_text}'")
                
                if any(keyword in button_text for keyword in ['Export', 'CSV', 'Excel', 'Personnalisé']):
                    export_buttons_found.append(button_text)
                    print(f"{indent}  ✅ BOUTON D'EXPORT DÉTECTÉ!")
            
            # Rechercher dans les enfants
            try:
                for child in widget.winfo_children():
                    find_export_buttons_in_tab(child, level + 1)
            except:
                pass
        
        # Lancer la recherche dans l'onglet Consommations
        find_export_buttons_in_tab(consommation_frame)
        
        # Résultats
        print(f"\n=== RÉSULTATS ===")
        print(f"Boutons d'export trouvés dans l'application principale: {len(export_buttons_found)}")
        
        for i, button_text in enumerate(export_buttons_found, 1):
            print(f"  {i}. {button_text}")
        
        if len(export_buttons_found) >= 3:
            print("✅ SUCCÈS: Tous les boutons d'export sont présents dans l'application principale!")
            success = True
        else:
            print("❌ PROBLÈME: Boutons d'export manquants dans l'application principale!")
            success = False
        
        # Vérifier le type de frame
        print(f"\nType du frame Consommations: {type(consommation_frame)}")
        
        # Afficher l'application pour inspection visuelle
        print(f"\n=== INSPECTION VISUELLE ===")
        print("Application principale ouverte avec l'onglet Consommations sélectionné.")
        print("Vérifiez manuellement la présence des boutons d'export.")
        print("Fermez l'application pour terminer le test.")
        
        # Centrer la fenêtre
        app.update_idletasks()
        x = (app.winfo_screenwidth() // 2) - (1000 // 2)
        y = (app.winfo_screenheight() // 2) - (700 // 2)
        app.geometry(f"1000x700+{x}+{y}")
        
        # Lancer la boucle d'événements
        app.mainloop()
        
        return success
        
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_main_app_export_buttons()
    
    if success:
        print("\n🎉 TEST RÉUSSI: Les boutons d'export sont présents dans l'application principale!")
    else:
        print("\n❌ TEST ÉCHOUÉ: Problème avec les boutons d'export dans l'application principale!")

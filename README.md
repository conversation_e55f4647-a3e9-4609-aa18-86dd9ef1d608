# Gestion MUN - Interface PyQt6/PySide6

Application de gestion des consommations avec interface graphique développée en PyQt6/PySide6.

## Fonctionnalités

- **Gestion des Secteurs** : Ajout, modification, suppression des secteurs
- **Gestion des Unités** : Gestion des unités avec liaison aux secteurs
- **Gestion des Catégories de Pièces** : Gestion des catégories (Petite C, Grande C)
- **Gestion des Pièces** : Gestion des pièces avec liaison aux catégories
- **Gestion des Périodes** : Gestion des périodes avec dates et mois
- **Gestion des Consommations** : Gestion complète des consommations avec calcul automatique des restes

## Caractéristiques de l'interface

- **Auto-complétion** : Tous les champs de sélection supportent l'auto-complétion
- **Suggestions** : Boutons de suggestion pour afficher le contenu des tables liées
- **Recherche** : Fonction de recherche dans tous les tableaux
- **Validation** : Validation des données en temps réel
- **Interface moderne** : Design moderne avec PyQt6

## Structure du projet

```
gestion MUN/
├── main.py                    # Point d'entrée de l'application
├── config.py                  # Configuration de l'application
├── requirements.txt           # Dépendances Python
├── install_and_run.py        # Script d'installation et de test
├── README.md                 # Ce fichier
└── src/
    ├── database/
    │   ├── __init__.py
    │   └── connection.py      # Gestion de la connexion MySQL
    ├── models/
    │   ├── __init__.py
    │   ├── base_model.py      # Modèle de base
    │   ├── secteur.py         # Modèle Secteur
    │   ├── unite.py           # Modèle Unité
    │   ├── categorie_piece.py # Modèle Catégorie Pièce
    │   ├── piece.py           # Modèle Pièce
    │   ├── periode.py         # Modèle Période
    │   └── consommation.py    # Modèle Consommation
    └── ui/
        ├── __init__.py
        ├── main_window.py     # Fenêtre principale
        └── widgets/
            ├── __init__.py
            ├── base_widget.py           # Widget de base
            ├── secteur_widget.py        # Widget Secteurs
            ├── unite_widget.py          # Widget Unités
            ├── categorie_piece_widget.py # Widget Catégories
            ├── piece_widget.py          # Widget Pièces
            ├── periode_widget.py        # Widget Périodes
            └── consommation_widget.py   # Widget Consommations
```

## Prérequis

1. **Python 3.8+**
2. **MySQL Server** avec la base de données `gestion_mun2` créée
3. **Tables de base de données** créées selon le schéma fourni

## Installation et Configuration

### 1. Configuration de la base de données

Modifiez le fichier `config.py` pour adapter la configuration à votre environnement :

```python
DATABASE_CONFIG = {
    'host': 'localhost',        # Votre serveur MySQL
    'user': 'root',            # Votre utilisateur MySQL
    'password': 'votre_mdp',   # Votre mot de passe MySQL
    'database': 'gestion_mun2', # Nom de votre base de données
    'charset': 'utf8mb4',
    'autocommit': True
}
```

### 2. Installation automatique

Utilisez le script d'installation automatique :

```bash
python install_and_run.py
```

Ce script va :
- Installer toutes les dépendances
- Vérifier la configuration
- Tester la connexion à la base de données
- Proposer de lancer l'application

### 3. Installation manuelle

Si vous préférez installer manuellement :

```bash
# Installer les dépendances
pip install -r requirements.txt

# Lancer l'application
python main.py
```

## Utilisation

### Interface principale

L'application s'ouvre avec une interface à onglets :

1. **Secteurs** : Gérez les secteurs de votre organisation
2. **Unités** : Gérez les unités rattachées aux secteurs
3. **Catégories Pièces** : Gérez les catégories de pièces (Petite C, Grande C)
4. **Pièces** : Gérez les pièces rattachées aux catégories
5. **Périodes** : Gérez les périodes avec dates et mois
6. **Consommations** : Gérez les consommations avec calcul automatique

### Fonctionnalités communes

- **Ajouter** : Bouton pour ajouter un nouvel élément
- **Modifier** : Double-clic ou bouton pour modifier un élément sélectionné
- **Supprimer** : Bouton pour supprimer un élément sélectionné (avec confirmation)
- **Rechercher** : Champ de recherche en temps réel
- **Actualiser** : Bouton pour recharger les données

### Auto-complétion et suggestions

- Tous les champs de sélection (ComboBox) supportent la saisie avec auto-complétion
- Les données sont automatiquement filtrées pendant la saisie
- Les relations entre tables sont automatiquement gérées

### Gestion des consommations

Le module de consommation offre des fonctionnalités avancées :

- **Sélection assistée** : Auto-complétion pour unités, pièces et périodes
- **Calcul automatique** : Le reste non consommé est calculé automatiquement
- **Validation** : Vérification des quantités et des relations
- **Affichage coloré** : Le reste est affiché en couleur selon sa valeur

## Dépendances

- **PyQt6** : Interface graphique moderne
- **PySide6** : Alternative à PyQt6 (optionnel)
- **mysql-connector-python** : Connexion à MySQL

## Versions disponibles

### Version PyQt6/PySide6 (Recommandée)
```bash
python main.py
```
Interface moderne avec toutes les fonctionnalités avancées.

### Version Tkinter (Alternative)
```bash
python app_tkinter.py
```
Interface simple et fiable, compatible avec tous les systèmes.

## Tests

### Test complet
```bash
python test_simple.py
```

### Test de la base de données uniquement
```bash
python test_database_only.py
```

## Dépannage

### Erreur de connexion à la base de données

1. Vérifiez que MySQL est démarré
2. Vérifiez les paramètres dans `config.py`
3. Assurez-vous que la base de données `gestion_mun2` existe
4. Vérifiez les permissions de l'utilisateur MySQL

### Problèmes d'interface graphique

1. **PyQt6/PySide6 ne fonctionne pas :**
   - Utilisez la version Tkinter : `python app_tkinter.py`
   - Réinstallez : `pip install --force-reinstall PySide6`

2. **Erreur NumPy :**
   ```bash
   pip install "numpy<2"
   ```

3. **DLL load failed (Windows) :**
   - Utilisez la version Tkinter comme alternative

### Erreurs de dépendances

```bash
# Réinstaller toutes les dépendances
pip install --force-reinstall -r requirements.txt

# Ou installer manuellement
pip install PySide6 mysql-connector-python
```

## Documentation

- **README.md** - Ce fichier
- **GUIDE_UTILISATION.md** - Guide détaillé d'utilisation
- **config.py** - Configuration de l'application

## Support

Pour toute question ou problème :

1. Consultez le **GUIDE_UTILISATION.md**
2. Testez avec `python test_database_only.py`
3. Vérifiez la configuration dans `config.py`
4. Utilisez la version Tkinter en cas de problème d'interface

## État du projet

✅ **Base de données** - Fonctionnelle
✅ **Modèles de données** - Complets
✅ **Interface Tkinter** - Fonctionnelle
⚠️ **Interface PyQt6/PySide6** - Problèmes de compatibilité sur certains systèmes
✅ **Tests** - Disponibles
✅ **Documentation** - Complète

## Licence

Ce projet est développé pour la gestion MUN.

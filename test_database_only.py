#!/usr/bin/env python3
"""
Test de la base de données uniquement
"""

import sys
import os

# Ajouter le répertoire src au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_database_connection():
    """Teste la connexion à la base de données"""
    print("Test de connexion à la base de données...")
    
    try:
        from database.connection import db_connection
        
        if db_connection.connect():
            print("✓ Connexion à la base de données réussie")
            
            # Test d'une requête simple
            result = db_connection.execute_query("SHOW TABLES")
            if result:
                print(f"✓ Tables trouvées: {len(result)}")
                for table in result:
                    print(f"  - {list(table.values())[0]}")
            else:
                print("⚠ Aucune table trouvée")
            
            db_connection.disconnect()
            return True
        else:
            print("✗ Échec de la connexion à la base de données")
            return False
    except Exception as e:
        print(f"✗ Erreur de base de données: {e}")
        return False

def test_models():
    """Teste les modèles de base de données"""
    print("\nTest des modèles...")
    
    try:
        from models.secteur import SecteurModel
        from models.unite import UniteModel
        from models.categorie_piece import CategoriePieceModel
        from models.piece import PieceModel
        from models.periode import PeriodeModel
        from models.consommation import ConsommationModel
        
        print("✓ Tous les modèles importés avec succès")
        
        # Test du modèle Secteur
        secteur_model = SecteurModel()
        secteurs = secteur_model.get_all()
        print(f"✓ Secteurs trouvés: {len(secteurs)}")
        
        # Test du modèle Unité
        unite_model = UniteModel()
        unites = unite_model.get_all()
        print(f"✓ Unités trouvées: {len(unites)}")
        
        # Test du modèle Catégorie Pièce
        categorie_model = CategoriePieceModel()
        categories = categorie_model.get_all()
        print(f"✓ Catégories trouvées: {len(categories)}")
        
        # Test du modèle Pièce
        piece_model = PieceModel()
        pieces = piece_model.get_all()
        print(f"✓ Pièces trouvées: {len(pieces)}")
        
        # Test du modèle Période
        periode_model = PeriodeModel()
        periodes = periode_model.get_all()
        print(f"✓ Périodes trouvées: {len(periodes)}")
        
        # Test du modèle Consommation
        consommation_model = ConsommationModel()
        consommations = consommation_model.get_all()
        print(f"✓ Consommations trouvées: {len(consommations)}")
        
        return True
    except Exception as e:
        print(f"✗ Erreur lors du test des modèles: {e}")
        return False

def test_crud_operations():
    """Teste les opérations CRUD de base"""
    print("\nTest des opérations CRUD...")
    
    try:
        from models.secteur import SecteurModel
        
        secteur_model = SecteurModel()
        
        # Test de création
        print("Test de création d'un secteur...")
        secteur_id = secteur_model.create("Test Secteur")
        if secteur_id:
            print(f"✓ Secteur créé avec ID: {secteur_id}")
            
            # Test de lecture
            secteur = secteur_model.get_by_id(secteur_id)
            if secteur:
                print(f"✓ Secteur lu: {secteur['nom_secteur']}")
                
                # Test de mise à jour
                success = secteur_model.update(secteur_id, "Test Secteur Modifié")
                if success:
                    print("✓ Secteur mis à jour")
                    
                    # Test de suppression
                    success = secteur_model.delete(secteur_id)
                    if success:
                        print("✓ Secteur supprimé")
                        return True
                    else:
                        print("✗ Échec de la suppression")
                else:
                    print("✗ Échec de la mise à jour")
            else:
                print("✗ Échec de la lecture")
        else:
            print("✗ Échec de la création")
        
        return False
    except Exception as e:
        print(f"✗ Erreur lors du test CRUD: {e}")
        return False

def show_database_summary():
    """Affiche un résumé de la base de données"""
    print("\n=== Résumé de la base de données ===")
    
    try:
        from models.secteur import SecteurModel
        from models.unite import UniteModel
        from models.categorie_piece import CategoriePieceModel
        from models.piece import PieceModel
        from models.periode import PeriodeModel
        from models.consommation import ConsommationModel
        
        # Compter les enregistrements
        secteur_model = SecteurModel()
        unite_model = UniteModel()
        categorie_model = CategoriePieceModel()
        piece_model = PieceModel()
        periode_model = PeriodeModel()
        consommation_model = ConsommationModel()
        
        secteurs = secteur_model.get_all()
        unites = unite_model.get_all()
        categories = categorie_model.get_all()
        pieces = piece_model.get_all()
        periodes = periode_model.get_all()
        consommations = consommation_model.get_all()
        
        print(f"Secteurs: {len(secteurs)}")
        print(f"Unités: {len(unites)}")
        print(f"Catégories de pièces: {len(categories)}")
        print(f"Pièces: {len(pieces)}")
        print(f"Périodes: {len(periodes)}")
        print(f"Consommations: {len(consommations)}")
        
        # Afficher quelques exemples
        if secteurs:
            print(f"\nExemples de secteurs:")
            for secteur in secteurs[:3]:
                print(f"  - {secteur['nom_secteur']}")
        
        if pieces:
            print(f"\nExemples de pièces:")
            for piece in pieces[:3]:
                print(f"  - {piece['nom_piece']}")
        
    except Exception as e:
        print(f"Erreur lors de l'affichage du résumé: {e}")

def main():
    """Fonction principale"""
    print("=== Test de la base de données Gestion MUN ===\n")
    
    # Test de connexion
    if not test_database_connection():
        print("\nVeuillez vérifier votre configuration de base de données dans config.py")
        return
    
    # Test des modèles
    if not test_models():
        return
    
    # Test des opérations CRUD
    if not test_crud_operations():
        return
    
    print("\n✓ Tous les tests de base de données sont passés!")
    
    # Afficher le résumé
    show_database_summary()
    
    print("\nLa base de données fonctionne correctement.")
    print("Vous pouvez maintenant résoudre les problèmes d'interface graphique.")

if __name__ == "__main__":
    main()

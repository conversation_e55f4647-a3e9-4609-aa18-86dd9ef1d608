"""
Widget de base pour tous les widgets de gestion
"""

from qt_imports import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                       QTableWidget, QTableWidgetItem, QLineEdit, QLabel,
                       QMessageBox, QHeaderView, QAbstractItemView, Qt, pyqtSignal,
                       get_item_flags, get_selection_behavior)
from typing import List, Dict, Any

class BaseWidget(QWidget):
    """Widget de base pour la gestion des tables"""
    
    # Signal émis quand les données changent
    data_changed = pyqtSignal()
    
    def __init__(self, title: str, columns: List[str]):
        super().__init__()
        self.title = title
        self.columns = columns
        self.current_data = []
        self.init_ui()
    
    def init_ui(self):
        """Initialise l'interface utilisateur"""
        layout = QVBoxLayout(self)
        
        # Titre
        title_label = QLabel(f"Gestion des {self.title}")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; margin: 10px;")
        layout.addWidget(title_label)
        
        # Barre de recherche
        search_layout = QHBoxLayout()
        search_label = QLabel("Rechercher:")
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText(f"Rechercher dans {self.title.lower()}...")
        self.search_input.textChanged.connect(self.filter_table)
        
        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_input)
        search_layout.addStretch()
        
        layout.addLayout(search_layout)
        
        # Boutons d'action
        button_layout = QHBoxLayout()
        
        self.add_button = QPushButton("Ajouter")
        self.add_button.clicked.connect(self.add_item)
        
        self.edit_button = QPushButton("Modifier")
        self.edit_button.clicked.connect(self.edit_item)
        self.edit_button.setEnabled(False)
        
        self.delete_button = QPushButton("Supprimer")
        self.delete_button.clicked.connect(self.delete_item)
        self.delete_button.setEnabled(False)
        
        self.refresh_button = QPushButton("Actualiser")
        self.refresh_button.clicked.connect(self.refresh_data)
        
        button_layout.addWidget(self.add_button)
        button_layout.addWidget(self.edit_button)
        button_layout.addWidget(self.delete_button)
        button_layout.addStretch()
        button_layout.addWidget(self.refresh_button)
        
        layout.addLayout(button_layout)
        
        # Tableau
        self.table = QTableWidget()
        self.table.setColumnCount(len(self.columns))
        self.table.setHorizontalHeaderLabels(self.columns)
        try:
            # PyQt6
            self.table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        except AttributeError:
            # PySide6
            self.table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.table.setAlternatingRowColors(True)
        self.table.horizontalHeader().setStretchLastSection(True)
        self.table.itemSelectionChanged.connect(self.on_selection_changed)
        self.table.itemDoubleClicked.connect(self.edit_item)
        
        layout.addWidget(self.table)
        
        # Charger les données initiales
        self.refresh_data()
    
    def on_selection_changed(self):
        """Gère le changement de sélection dans le tableau"""
        has_selection = len(self.table.selectedItems()) > 0
        self.edit_button.setEnabled(has_selection)
        self.delete_button.setEnabled(has_selection)
    
    def get_selected_row_data(self) -> Dict[str, Any]:
        """
        Récupère les données de la ligne sélectionnée
        
        Returns:
            Dictionnaire avec les données de la ligne
        """
        current_row = self.table.currentRow()
        if current_row >= 0 and current_row < len(self.current_data):
            return self.current_data[current_row]
        return {}
    
    def populate_table(self, data: List[Dict[str, Any]]):
        """
        Remplit le tableau avec les données
        
        Args:
            data: Liste des données à afficher
        """
        self.current_data = data
        self.table.setRowCount(len(data))
        
        for row, item in enumerate(data):
            for col, column_name in enumerate(self.get_table_columns()):
                value = item.get(column_name, "")
                if value is None:
                    value = ""
                
                table_item = QTableWidgetItem(str(value))
                try:
                    # PyQt6
                    table_item.setFlags(table_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
                except AttributeError:
                    # PySide6
                    table_item.setFlags(table_item.flags() & ~Qt.ItemIsEditable)
                self.table.setItem(row, col, table_item)
        
        # Ajuster la taille des colonnes
        self.table.resizeColumnsToContents()
    
    def filter_table(self):
        """Filtre le tableau selon le texte de recherche"""
        search_text = self.search_input.text().lower()
        
        for row in range(self.table.rowCount()):
            show_row = False
            for col in range(self.table.columnCount()):
                item = self.table.item(row, col)
                if item and search_text in item.text().lower():
                    show_row = True
                    break
            self.table.setRowHidden(row, not show_row)
    
    def show_message(self, title: str, message: str, msg_type: str = "info"):
        """
        Affiche un message à l'utilisateur
        
        Args:
            title: Titre du message
            message: Contenu du message
            msg_type: Type de message (info, warning, error)
        """
        if msg_type == "warning":
            QMessageBox.warning(self, title, message)
        elif msg_type == "error":
            QMessageBox.critical(self, title, message)
        else:
            QMessageBox.information(self, title, message)
    
    # Méthodes à implémenter dans les classes dérivées
    def get_table_columns(self) -> List[str]:
        """Retourne les noms des colonnes de la base de données"""
        raise NotImplementedError
    
    def refresh_data(self):
        """Actualise les données du tableau"""
        raise NotImplementedError
    
    def add_item(self):
        """Ajoute un nouvel élément"""
        raise NotImplementedError
    
    def edit_item(self):
        """Modifie l'élément sélectionné"""
        raise NotImplementedError
    
    def delete_item(self):
        """Supprime l'élément sélectionné"""
        raise NotImplementedError

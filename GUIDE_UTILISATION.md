# Guide d'utilisation - Application Gestion MUN

## Vue d'ensemble

L'application Gestion MUN est une interface graphique pour gérer les données de consommation de votre base de données MySQL. Elle offre deux versions :

1. **Version PyQt6/PySide6** (recommandée) - Interface moderne
2. **Version Tkinter** (alternative) - Interface simple et fiable

## Installation et Configuration

### 1. Prérequis

- Python 3.8 ou supérieur
- MySQL Server avec la base de données `gestion_mun2`
- Tables créées selon le schéma fourni

### 2. Configuration de la base de données

Modifiez le fichier `config.py` :

```python
DATABASE_CONFIG = {
    'host': 'localhost',        # Votre serveur MySQL
    'user': 'root',            # Votre utilisateur MySQL
    'password': 'votre_mdp',   # Votre mot de passe MySQL
    'database': 'gestion_mun2', # Nom de votre base de données
    'charset': 'utf8mb4',
    'autocommit': True
}
```

### 3. Installation des dépendances

```bash
# Installation automatique
python install_and_run.py

# Ou installation manuelle
pip install -r requirements.txt
```

### 4. Test de l'installation

```bash
# Test complet
python test_simple.py

# Test de la base de données uniquement
python test_database_only.py
```

## Lancement de l'application

### Version recommandée (PyQt6/PySide6)

```bash
python main.py
```

### Version alternative (Tkinter)

Si vous rencontrez des problèmes avec PyQt6/PySide6 :

```bash
python app_tkinter.py
```

## Utilisation de l'interface

### Structure de l'application

L'application est organisée en onglets :

1. **Secteurs** - Gestion des secteurs
2. **Unités** - Gestion des unités rattachées aux secteurs
3. **Catégories** - Gestion des catégories de pièces
4. **Pièces** - Gestion des pièces rattachées aux catégories
5. **Périodes** - Gestion des périodes avec dates et mois
6. **Consommations** - Gestion des consommations avec calcul automatique

### Fonctionnalités communes

#### Boutons d'action
- **Ajouter** : Crée un nouvel enregistrement
- **Modifier** : Modifie l'enregistrement sélectionné
- **Supprimer** : Supprime l'enregistrement sélectionné (avec confirmation)
- **Actualiser** : Recharge les données depuis la base

#### Recherche
- Champ de recherche en temps réel
- Filtre automatique pendant la saisie
- Recherche dans tous les champs visibles

#### Navigation
- Sélection par clic simple
- Modification par double-clic
- Tri par colonnes (clic sur l'en-tête)

### Gestion des Secteurs

**Fonctionnalités :**
- Ajout de nouveaux secteurs
- Modification du nom des secteurs
- Suppression (attention : supprime aussi les unités associées)

**Champs :**
- Nom du secteur (obligatoire)

### Gestion des Unités

**Fonctionnalités :**
- Ajout d'unités avec sélection du secteur
- Auto-complétion pour la sélection du secteur
- Affichage avec nom du secteur

**Champs :**
- Nom de l'unité (obligatoire)
- Secteur (sélection avec auto-complétion)

### Gestion des Catégories de Pièces

**Fonctionnalités :**
- Ajout de catégories prédéfinies
- Modification des catégories existantes

**Valeurs possibles :**
- Petite C
- Grande C

### Gestion des Pièces

**Fonctionnalités :**
- Ajout de pièces avec sélection de catégorie
- Auto-complétion pour la sélection de catégorie
- Affichage avec nom de la catégorie

**Champs :**
- Nom de la pièce (obligatoire)
- Catégorie (sélection avec auto-complétion)

### Gestion des Périodes

**Fonctionnalités :**
- Ajout de périodes avec date et mois
- Sélection de date avec calendrier
- Validation automatique du mois (1-12)

**Champs :**
- Date de la période (sélecteur de date)
- Mois (sélection 1-12)

### Gestion des Consommations

**Fonctionnalités avancées :**
- Sélection assistée avec auto-complétion
- Calcul automatique du reste non consommé
- Validation des quantités
- Affichage coloré du reste

**Champs :**
- Unité (sélection avec auto-complétion)
- Pièce (sélection avec auto-complétion)
- Période (sélection avec auto-complétion)
- Nombre attribué (numérique)
- Nombre consommé (numérique)
- Reste non consommé (calculé automatiquement)

**Calcul automatique :**
- Reste = Nombre attribué - Nombre consommé
- Couleur verte : reste = 0
- Couleur bleue : reste > 0
- Couleur rouge : reste < 0 (surconsommation)

## Résolution des problèmes

### Erreur de connexion à la base de données

1. Vérifiez que MySQL est démarré
2. Vérifiez les paramètres dans `config.py`
3. Assurez-vous que la base `gestion_mun2` existe
4. Vérifiez les permissions de l'utilisateur MySQL

### Problèmes d'interface graphique

1. **PyQt6/PySide6 ne fonctionne pas :**
   - Utilisez la version Tkinter : `python app_tkinter.py`
   - Réinstallez les dépendances : `pip install --force-reinstall PySide6`

2. **Erreur NumPy :**
   ```bash
   pip install "numpy<2"
   ```

3. **DLL load failed :**
   - Problème de compatibilité Windows
   - Utilisez la version Tkinter comme alternative

### Problèmes de données

1. **Tables vides :**
   - Vérifiez que les tables sont créées
   - Ajoutez des données de test

2. **Erreurs de clés étrangères :**
   - Respectez l'ordre de création : Secteurs → Unités → Consommations
   - Vérifiez que les enregistrements liés existent

## Conseils d'utilisation

### Ordre recommandé de saisie

1. **Secteurs** - Créez d'abord vos secteurs
2. **Unités** - Ajoutez les unités dans chaque secteur
3. **Catégories** - Créez les catégories de pièces
4. **Pièces** - Ajoutez les pièces dans chaque catégorie
5. **Périodes** - Définissez vos périodes de gestion
6. **Consommations** - Saisissez les consommations

### Bonnes pratiques

- **Sauvegardez régulièrement** votre base de données
- **Testez les suppressions** sur des données de test d'abord
- **Utilisez la recherche** pour trouver rapidement des enregistrements
- **Vérifiez les calculs** de consommation avant validation

### Raccourcis

- **F5** : Actualiser les données
- **Ctrl+Q** : Quitter l'application
- **Double-clic** : Modifier un enregistrement
- **Entrée** : Valider un formulaire

## Support technique

En cas de problème :

1. Consultez les logs d'erreur dans la console
2. Vérifiez la configuration de la base de données
3. Testez avec `python test_database_only.py`
4. Utilisez la version Tkinter en cas de problème d'interface

## Évolutions futures

Fonctionnalités prévues :
- Export des données (Excel, PDF)
- Rapports de consommation
- Graphiques et statistiques
- Gestion des utilisateurs
- Historique des modifications

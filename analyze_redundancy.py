#!/usr/bin/env python3
"""
Analyse de la redondance dans toutes les tables de la base de données
"""

import sys
import os

# Ajouter le répertoire src au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def analyze_table_structure():
    """Analyse la structure de toutes les tables"""
    print("=== Analyse de la redondance dans toutes les tables ===")
    
    try:
        from database.connection import db_connection
        
        if not db_connection.connect():
            print("Erreur de connexion à la base de données")
            return
        
        # Analyser chaque table
        tables = ['Secteur', 'Unite', 'CategoriePiece', 'Piece', 'Periode', 'Consommation']
        
        for table in tables:
            print(f"\n=== Table {table} ===")
            
            # Obtenir la structure de la table
            query_structure = f"DESCRIBE {table}"
            structure = db_connection.execute_query(query_structure)
            
            print("Structure de la table:")
            for col in structure:
                print(f"  {col['Field']} - {col['Type']} - {col['Null']} - {col['Key']}")
            
            # Obtenir quelques exemples de données
            query_data = f"SELECT * FROM {table} LIMIT 3"
            data = db_connection.execute_query(query_data)
            
            print("Exemples de données:")
            if data:
                # Afficher les noms des colonnes
                columns = list(data[0].keys())
                print(f"  Colonnes: {columns}")
                
                # Afficher les premières lignes
                for i, row in enumerate(data):
                    print(f"  Ligne {i+1}: {dict(row)}")
            else:
                print("  Aucune donnée trouvée")
        
        db_connection.disconnect()
        
    except Exception as e:
        print(f"Erreur: {e}")
        import traceback
        traceback.print_exc()

def analyze_data_redundancy():
    """Analyse la redondance dans les données"""
    print("\n=== Analyse de la redondance des données ===")
    
    try:
        from models.secteur import SecteurModel
        from models.unite import UniteModel
        from models.categorie_piece import CategoriePieceModel
        from models.piece import PieceModel
        from models.periode import PeriodeModel
        from models.consommation import ConsommationModel
        
        # Analyser les secteurs
        print("\n--- Secteurs ---")
        secteur_model = SecteurModel()
        secteurs = secteur_model.get_all()
        print(f"Nombre total: {len(secteurs)}")
        
        # Vérifier les doublons par nom
        noms_secteurs = [s.get('nom_secteur', '') for s in secteurs]
        noms_uniques = set(noms_secteurs)
        if len(noms_secteurs) != len(noms_uniques):
            print(f"⚠️ Doublons détectés: {len(noms_secteurs) - len(noms_uniques)} secteurs en double")
            # Trouver les doublons
            from collections import Counter
            compteur = Counter(noms_secteurs)
            doublons = [nom for nom, count in compteur.items() if count > 1]
            print(f"Secteurs en double: {doublons}")
        else:
            print("✅ Aucun doublon détecté")
        
        # Analyser les unités
        print("\n--- Unités ---")
        unite_model = UniteModel()
        unites = unite_model.get_all()
        print(f"Nombre total: {len(unites)}")
        
        # Vérifier les doublons par nom
        noms_unites = [u.get('nom_unite', '') for u in unites]
        noms_uniques = set(noms_unites)
        if len(noms_unites) != len(noms_uniques):
            print(f"⚠️ Doublons détectés: {len(noms_unites) - len(noms_uniques)} unités en double")
            from collections import Counter
            compteur = Counter(noms_unites)
            doublons = [nom for nom, count in compteur.items() if count > 1]
            print(f"Unités en double: {doublons[:10]}")  # Afficher les 10 premiers
        else:
            print("✅ Aucun doublon détecté")
        
        # Analyser les catégories
        print("\n--- Catégories ---")
        categorie_model = CategoriePieceModel()
        categories = categorie_model.get_all()
        print(f"Nombre total: {len(categories)}")
        
        # Vérifier les catégories vides ou invalides
        categories_valides = [c for c in categories if c.get('nom_categorie') and c.get('nom_categorie').strip()]
        categories_vides = [c for c in categories if not c.get('nom_categorie') or not c.get('nom_categorie').strip()]
        
        print(f"Catégories valides: {len(categories_valides)}")
        print(f"Catégories vides/invalides: {len(categories_vides)}")
        
        if categories_vides:
            print("Catégories à nettoyer:")
            for cat in categories_vides:
                print(f"  ID: {cat.get('id_categorie')}, Nom: '{cat.get('nom_categorie')}'")
        
        # Analyser les pièces
        print("\n--- Pièces ---")
        piece_model = PieceModel()
        pieces = piece_model.get_all()
        print(f"Nombre total: {len(pieces)}")
        
        # Vérifier les doublons par nom
        noms_pieces = [p.get('nom_piece', '') for p in pieces]
        noms_uniques = set(noms_pieces)
        if len(noms_pieces) != len(noms_uniques):
            print(f"⚠️ Doublons détectés: {len(noms_pieces) - len(noms_uniques)} pièces en double")
            from collections import Counter
            compteur = Counter(noms_pieces)
            doublons = [nom for nom, count in compteur.items() if count > 1]
            print(f"Pièces en double: {doublons[:10]}")  # Afficher les 10 premiers
        else:
            print("✅ Aucun doublon détecté")
        
        # Analyser les périodes
        print("\n--- Périodes ---")
        periode_model = PeriodeModel()
        periodes = periode_model.get_all()
        print(f"Nombre total: {len(periodes)}")
        
        # Vérifier les doublons par date
        dates_periodes = [str(p.get('date_periode', '')) for p in periodes]
        dates_uniques = set(dates_periodes)
        if len(dates_periodes) != len(dates_uniques):
            print(f"⚠️ Doublons détectés: {len(dates_periodes) - len(dates_uniques)} périodes en double")
        else:
            print("✅ Aucun doublon détecté")
        
        # Analyser les consommations
        print("\n--- Consommations ---")
        consommation_model = ConsommationModel()
        consommations = consommation_model.get_all()
        print(f"Nombre total: {len(consommations)}")
        
    except Exception as e:
        print(f"Erreur: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Fonction principale"""
    print("=== Analyse complète de la redondance ===\n")
    
    analyze_table_structure()
    analyze_data_redundancy()
    
    print("\n=== Recommandations ===")
    print("1. Supprimer les catégories vides/invalides")
    print("2. Fusionner ou supprimer les doublons détectés")
    print("3. Nettoyer les données incohérentes")
    print("4. Optimiser l'affichage dans l'interface")

if __name__ == "__main__":
    main()

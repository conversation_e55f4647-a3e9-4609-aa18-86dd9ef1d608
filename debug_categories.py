#!/usr/bin/env python3
"""
Debug des catégories pour comprendre le problème
"""

import sys
import os

# Ajouter le répertoire src au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def debug_categories():
    """Debug des catégories"""
    print("=== Debug des catégories ===")
    
    try:
        from models.categorie_piece import CategoriePieceModel
        from models.piece import PieceModel
        
        # Vérifier les catégories
        categorie_model = CategoriePieceModel()
        categories = categorie_model.get_all()
        
        print(f"Nombre de catégories: {len(categories)}")
        print("Catégories trouvées:")
        for cat in categories:
            print(f"  ID: {cat.get('id_categorie')}, Nom: '{cat.get('nom_categorie')}'")
        
        # Vérifier quelques pièces individuellement
        piece_model = PieceModel()
        pieces = piece_model.get_all()
        
        print(f"\nNombre de pièces: {len(pieces)}")
        print("Premières pièces:")
        for i, piece in enumerate(pieces[:3]):
            print(f"  Pièce {i+1}: {piece}")
            
        # Test de la requête directe
        print("\n=== Test de la requête directe ===")
        from database.connection import db_connection
        
        if db_connection.connect():
            # Requête directe pour voir les données
            query = """
            SELECT p.id_piece, p.nom_piece, p.id_categorie, c.nom_categorie
            FROM Piece p
            LEFT JOIN CategoriePiece c ON p.id_categorie = c.id_categorie
            LIMIT 5
            """
            result = db_connection.execute_query(query)
            
            print("Résultat de la requête directe:")
            for row in result:
                print(f"  {row}")
            
            db_connection.disconnect()
        
    except Exception as e:
        print(f"Erreur: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_categories()

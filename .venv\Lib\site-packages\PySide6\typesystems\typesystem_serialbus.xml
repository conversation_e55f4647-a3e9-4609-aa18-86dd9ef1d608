<?xml version="1.0" encoding="UTF-8"?>
<!--
// Copyright (C) 2022 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
-->

<typesystem package="PySide6.QtSerialBus">
    <load-typesystem name="typesystem_core.xml" generate="no"/>
    <load-typesystem name="typesystem_network.xml" generate="no"/>
    <load-typesystem name="typesystem_serialport.xml" generate="no"/>

    <object-type name="QCanBus">
        <!-- Remove errorMessage argument, return tuple instead.  -->
        <modify-function signature="availableDevices(QString,QString*)const">
            <modify-argument index="2">
                <remove-default-expression/>
                <remove-argument/>
            </modify-argument>
            <modify-argument index="return">
                <replace-type modified-type="tuple"/>
            </modify-argument>
            <inject-code class="target" position="beginning"
                         file="../glue/qtserialbus.cpp" snippet="qcanbus-available-devices"/>
            <inject-documentation format="target" mode="append">
            The function returns a tuple of (device_list, error_string).
            </inject-documentation>
        </modify-function>
        <modify-function signature="createDevice(QString,QString,QString*)const">
            <modify-argument index="3">
                <remove-default-expression/>
                <remove-argument/>
            </modify-argument>
            <modify-argument index="return"> <!-- Suppress return value heuristics -->
                <define-ownership class="target" owner="default"/>
                <replace-type modified-type="tuple"/>
            </modify-argument>
            <inject-code class="target" position="beginning"
                         file="../glue/qtserialbus.cpp" snippet="qcanbus-createdevice"/>
            <inject-documentation format="target" mode="append">
            The function returns a tuple of (device, error_string).
            </inject-documentation>
        </modify-function>
    </object-type>
    <object-type name="QCanBusDevice">
        <enum-type name="CanBusError"/>
        <enum-type name="CanBusDeviceState"/>
        <enum-type name="CanBusStatus"/>
        <enum-type name="ConfigurationKey"/>
        <enum-type name="Direction" flags="Directions"/>
        <modify-function signature="deviceInfo()const" access="final"/>
        <value-type name="Filter">
            <enum-type name="FormatFilter" flags="FormatFilters"/>
        </value-type>
    </object-type>
    <value-type name="QCanBusDeviceInfo"> <!-- deleted default constructor -->
        <modify-function signature="swap(QCanBusDeviceInfo&amp;)" remove="all"/>
    </value-type>
    <object-type name="QCanBusFactory"/>
    <value-type name="QCanBusFrame">
        <enum-type name="FrameType"/>
        <enum-type name="FrameError" flags="FrameErrors"/>
        <value-type name="TimeStamp"/>
    </value-type>
    <object-type name="QModbusClient"/>
    <value-type name="QModbusDataUnit">
        <enum-type name="RegisterType"/>
    </value-type>
    <object-type name="QModbusDevice">
        <enum-type name="Error"/>
        <enum-type name="State"/>
        <enum-type name="ConnectionParameter"/>
        <enum-type name="IntermediateError"/>
    </object-type>
    <value-type name="QModbusDeviceIdentification">
        <enum-type name="ObjectId"/>
        <enum-type name="ReadDeviceIdCode"/>
        <enum-type name="ConformityLevel"/>
    </value-type>
    <object-type name="QModbusPdu">
        <enum-type name="ExceptionCode"/>
        <enum-type name="FunctionCode"/>
        <modify-field name="ExceptionByte" remove="true"/> <!-- Link error -->
    </object-type>
    <object-type name="QModbusReply">
        <enum-type name="ReplyType"/>
    </object-type>
    <object-type name="QModbusRequest"/>
    <object-type name="QModbusRtuSerialClient"/>
    <object-type name="QModbusRtuSerialServer"/>
    <object-type name="QModbusServer">
        <enum-type name="Option"/>
    </object-type>
    <object-type name="QModbusTcpClient"/>
    <object-type name="QModbusTcpConnectionObserver"/>
    <object-type name="QModbusTcpServer"/>
</typesystem>

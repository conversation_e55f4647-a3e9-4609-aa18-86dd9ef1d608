#!/usr/bin/env python3
"""
Script pour forcer le redémarrage de l'application avec les nouveaux boutons d'export
"""

import sys
import os
import subprocess
import time

def force_restart_app():
    """Force le redémarrage de l'application"""
    print("=== Redémarrage forcé de l'application ===")
    
    try:
        # Tuer tous les processus Python existants (optionnel)
        print("Nettoyage des processus Python existants...")
        
        # Attendre un peu
        time.sleep(2)
        
        # Relancer l'application
        print("Relancement de l'application avec les nouveaux boutons d'export...")
        
        # Changer vers le répertoire de l'application
        app_dir = os.path.dirname(os.path.abspath(__file__))
        os.chdir(app_dir)
        
        # Lancer l'application
        subprocess.run([sys.executable, "app_tkinter.py"], check=True)
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du redémarrage: {e}")
        return False

def create_verification_guide():
    """Crée un guide de vérification pour l'utilisateur"""
    guide_content = """
# 🔍 GUIDE DE VÉRIFICATION DES BOUTONS D'EXPORT

## ✅ ÉTAPES DE VÉRIFICATION

### 1. Aller dans l'onglet Consommations
- Cliquez sur l'onglet "Consommations" (5ème onglet)

### 2. Vérifier la barre de boutons
Vous devriez voir cette séquence de boutons :
```
[Ajouter] [Modifier] [Supprimer] | [📊 Export CSV] [📈 Export Excel] [⚙️ Export Personnalisé] [Actualiser]
```

### 3. Vérifier l'ordre des colonnes
L'affichage doit montrer les colonnes dans cet ordre :
1. Secteur
2. Unité  ← NOUVELLE COLONNE
3. Pièce
4. Catégorie
5. Date
6. Nombre Attribué
7. Nombre Consommé
8. Reste Non Consommé

### 4. Tester les boutons d'export
- **📊 Export CSV** : Doit ouvrir une boîte de dialogue de sauvegarde
- **📈 Export Excel** : Doit ouvrir une boîte de dialogue de sauvegarde
- **⚙️ Export Personnalisé** : Doit ouvrir une fenêtre de sélection des champs

## 🚨 SI LES BOUTONS NE SONT PAS VISIBLES

### Solution 1 : Actualiser l'onglet
- Cliquez sur le bouton "Actualiser" dans l'onglet Consommations

### Solution 2 : Redémarrer l'application
- Fermez complètement l'application
- Relancez-la avec : `python app_tkinter.py`

### Solution 3 : Vérifier la largeur de la fenêtre
- Agrandissez la fenêtre horizontalement
- Les boutons pourraient être cachés si la fenêtre est trop étroite

## ✅ CONFIRMATION DU SUCCÈS

Si vous voyez :
- ✅ Les 3 boutons d'export avec leurs icônes
- ✅ La colonne "Unité" en 2ème position
- ✅ L'ordre correct des colonnes

Alors toutes les modifications ont été appliquées avec succès !

## 📞 SUPPORT

Si les boutons ne sont toujours pas visibles, le problème pourrait venir de :
1. Cache Python (.pyc files)
2. Problème d'affichage de la fenêtre
3. Conflit avec une ancienne version

Solution : Supprimez les fichiers .pyc et redémarrez.
"""
    
    with open("GUIDE_VERIFICATION_EXPORT.md", "w", encoding="utf-8") as f:
        f.write(guide_content)
    
    print("✅ Guide de vérification créé : GUIDE_VERIFICATION_EXPORT.md")

if __name__ == "__main__":
    print("🚀 Préparation du redémarrage de l'application...")
    
    # Créer le guide de vérification
    create_verification_guide()
    
    print("\n📋 INSTRUCTIONS :")
    print("1. L'application va se relancer automatiquement")
    print("2. Allez dans l'onglet 'Consommations'")
    print("3. Vérifiez la présence des boutons d'export")
    print("4. Consultez GUIDE_VERIFICATION_EXPORT.md pour plus de détails")
    
    input("\nAppuyez sur Entrée pour relancer l'application...")
    
    success = force_restart_app()
    
    if success:
        print("\n🎉 Application relancée avec succès!")
        print("Vérifiez maintenant les boutons d'export dans l'onglet Consommations.")
    else:
        print("\n❌ Problème lors du relancement.")
        print("Essayez de lancer manuellement : python app_tkinter.py")

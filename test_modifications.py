#!/usr/bin/env python3
"""
Test des modifications pour vérifier l'affichage des noms au lieu des IDs
"""

import sys
import os

# Ajouter le répertoire src au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_piece_model():
    """Teste le modèle Piece avec noms de catégories"""
    print("=== Test du modèle Piece ===")
    
    try:
        from models.piece import PieceModel
        
        piece_model = PieceModel()
        pieces = piece_model.get_all()
        
        print(f"Nombre de pièces: {len(pieces)}")
        
        if pieces:
            print("\nPremières pièces avec catégories:")
            for i, piece in enumerate(pieces[:5]):
                print(f"  {i+1}. {piece}")
        
        return True
    except Exception as e:
        print(f"Erreur: {e}")
        return False

def test_unite_model():
    """Teste le modèle Unite avec noms de secteurs"""
    print("\n=== Test du modèle Unite ===")
    
    try:
        from models.unite import UniteModel
        
        unite_model = UniteModel()
        unites = unite_model.get_all()
        
        print(f"Nombre d'unités: {len(unites)}")
        
        if unites:
            print("\nPremières unités avec secteurs:")
            for i, unite in enumerate(unites[:5]):
                print(f"  {i+1}. {unite}")
        
        return True
    except Exception as e:
        print(f"Erreur: {e}")
        return False

def test_consommation_model():
    """Teste le modèle Consommation avec tous les détails"""
    print("\n=== Test du modèle Consommation ===")
    
    try:
        from models.consommation import ConsommationModel
        
        consommation_model = ConsommationModel()
        consommations = consommation_model.get_all()
        
        print(f"Nombre de consommations: {len(consommations)}")
        
        if consommations:
            print("\nPremières consommations avec détails:")
            for i, consommation in enumerate(consommations[:3]):
                print(f"  {i+1}. {consommation}")
        else:
            print("Aucune consommation trouvée (normal si pas encore ajoutées)")
        
        return True
    except Exception as e:
        print(f"Erreur: {e}")
        return False

def main():
    """Fonction principale"""
    print("=== Test des modifications d'affichage ===\n")
    
    success = True
    success &= test_piece_model()
    success &= test_unite_model()
    success &= test_consommation_model()
    
    if success:
        print("\n✅ Toutes les modifications fonctionnent correctement!")
        print("\nMaintenant dans l'application:")
        print("- L'onglet 'Pièces' affiche le nom de la catégorie au lieu de l'ID")
        print("- L'onglet 'Unités' affiche le nom du secteur au lieu de l'ID")
        print("- L'onglet 'Consommations' affiche tous les noms au lieu des IDs")
    else:
        print("\n❌ Certaines modifications ont échoué")

if __name__ == "__main__":
    main()

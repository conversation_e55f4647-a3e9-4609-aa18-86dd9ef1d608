#!/usr/bin/env python3
"""
Script pour corriger les imports matplotlib dans app_tkinter.py
"""

def fix_matplotlib_imports():
    """Corrige les imports matplotlib"""
    
    # <PERSON>re le fichier
    with open('app_tkinter.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Remplacer toutes les occurrences
    content = content.replace('FigureCanvasTkinter', 'FigureCanvasTkAgg')
    
    # Éc<PERSON>re le fichier corrigé
    with open('app_tkinter.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ Tous les imports matplotlib ont été corrigés")
    print("FigureCanvasTkinter → FigureCanvasTkAgg")

if __name__ == "__main__":
    fix_matplotlib_imports()

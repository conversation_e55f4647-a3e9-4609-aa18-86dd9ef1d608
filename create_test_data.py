#!/usr/bin/env python3
"""
Crée des données de test pour les statistiques
"""

import sys
import os
from datetime import date, timedelta
import random

# Ajouter le répertoire src au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def create_test_consommations():
    """Crée des consommations de test pour les statistiques"""
    print("=== Création de données de test pour les statistiques ===")
    
    try:
        from models.consommation import ConsommationModel
        from models.unite import UniteModel
        from models.piece import PieceModel
        from models.periode import PeriodeModel
        
        # Modèles
        consommation_model = ConsommationModel()
        unite_model = UniteModel()
        piece_model = PieceModel()
        periode_model = PeriodeModel()
        
        # Récupérer les données existantes
        unites = unite_model.get_all()
        pieces = piece_model.get_all()
        
        if not unites or not pieces:
            print("❌ Pas assez de données de base (unités ou pièces)")
            return False
        
        print(f"Unités disponibles: {len(unites)}")
        print(f"Pièces disponibles: {len(pieces)}")
        
        # Créer plusieurs périodes de test
        periodes_test = []
        base_date = date(2024, 1, 1)
        
        for i in range(6):  # 6 mois de données
            test_date = base_date + timedelta(days=30*i)
            mois = test_date.month
            
            periode_id = periode_model.create(test_date, mois)
            if periode_id:
                periodes_test.append(periode_id)
                print(f"Période créée: {test_date} (ID: {periode_id})")
        
        if not periodes_test:
            print("❌ Impossible de créer les périodes de test")
            return False
        
        # Créer des consommations variées
        consommations_creees = 0
        
        for _ in range(50):  # 50 consommations de test
            # Sélectionner aléatoirement
            unite = random.choice(unites)
            piece = random.choice(pieces)
            periode_id = random.choice(periodes_test)
            
            # Générer des quantités réalistes
            nombre_attribue = random.randint(50, 500)
            # Consommation entre 60% et 120% de l'attribué
            facteur_consommation = random.uniform(0.6, 1.2)
            nombre_consomme = int(nombre_attribue * facteur_consommation)
            
            # Créer la consommation
            consommation_id = consommation_model.create(
                unite['id_unite'],
                piece['id_piece'],
                periode_id,
                nombre_attribue,
                nombre_consomme
            )
            
            if consommation_id:
                consommations_creees += 1
        
        print(f"✅ {consommations_creees} consommations de test créées")
        
        # Vérifier le résultat
        all_consommations = consommation_model.get_all()
        print(f"Total consommations en base: {len(all_consommations)}")
        
        if len(all_consommations) >= consommations_creees:
            print("✅ Données de test créées avec succès!")
            print("\nExemples de données créées:")
            for i, cons in enumerate(all_consommations[:5]):
                print(f"  {i+1}. {cons.get('nom_unite')} - {cons.get('nom_piece')} - "
                      f"Attribué: {cons.get('nombre_attribue')}, "
                      f"Consommé: {cons.get('nombre_consomme')}, "
                      f"Reste: {cons.get('reste_non_consomme')}")
            
            return True
        else:
            print("⚠️ Problème lors de la création des données")
            return False
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Fonction principale"""
    print("=== Création de données de test pour les statistiques ===\n")
    
    response = input("Voulez-vous créer des données de test pour les statistiques? (o/n): ")
    if response.lower() not in ['o', 'oui', 'y', 'yes']:
        print("Opération annulée.")
        return
    
    success = create_test_consommations()
    
    if success:
        print("\n🎉 Données de test créées avec succès!")
        print("\nVous pouvez maintenant:")
        print("1. Relancer l'application")
        print("2. Aller dans l'onglet 'Statistiques'")
        print("3. Tester les différents graphiques:")
        print("   - 📊 Consommation par Unité")
        print("   - 🏢 Consommation par Secteur")
        print("   - 📅 Consommation par Période")
        print("   - 🔧 Consommation par Pièce")
        print("   - 📈 Évolution Temporelle")
        print("   - 🎯 Efficacité par Unité")
        print("   - 📋 Top 10 Consommateurs")
        print("   - ⚖️ Comparaison Secteurs")
        print("   - 📊 Répartition par Catégorie")
        print("   - 📉 Analyse des Restes")
    else:
        print("\n❌ Échec de la création des données de test")

if __name__ == "__main__":
    main()
